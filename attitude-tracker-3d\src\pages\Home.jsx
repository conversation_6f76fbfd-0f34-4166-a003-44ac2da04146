/**
 * 首页组件
 * 用途：系统介绍、功能导航和快速开始
 */

import React from 'react';

const Home = ({ onNavigate }) => {
  const features = [
    {
      icon: '📊',
      title: '数据输入',
      description: '支持手动输入或CSV文件上传姿态数据',
      action: () => onNavigate('input')
    },
    {
      icon: '🎯',
      title: '3D可视化',
      description: '实时三维姿态展示和动画播放',
      action: () => onNavigate('viewer')
    },
    {
      icon: '📋',
      title: '历史记录',
      description: '查看和管理历史姿态数据记录',
      action: () => onNavigate('history')
    }
  ];

  const quickStats = [
    { label: '支持格式', value: 'CSV, 手动输入' },
    { label: '角度单位', value: '度/弧度' },
    { label: '坐标系', value: 'XYZ 三维' },
    { label: '播放控制', value: '播放/暂停/重置' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* 主要内容区域 */}
      <div className="container mx-auto px-6 py-12">
        {/* 欢迎区域 */}
        <div className="text-center mb-16">
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-primary-600 rounded-full mb-6">
              <span className="text-3xl font-bold text-white">3D</span>
            </div>
            <h1 className="text-5xl font-bold text-white mb-4">
              Attitude Tracker 3D
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              基于Web的三维姿态追踪系统，支持实时数据可视化、动画播放和轨迹分析
            </p>
          </div>

          {/* 快速开始按钮 */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => onNavigate('input')}
              className="btn-primary text-lg px-8 py-3 flex items-center space-x-2"
            >
              <span>🚀</span>
              <span>快速开始</span>
            </button>
            <button
              onClick={() => onNavigate('viewer')}
              className="btn-secondary text-lg px-8 py-3 flex items-center space-x-2"
            >
              <span>👁️</span>
              <span>查看演示</span>
            </button>
          </div>
        </div>

        {/* 功能特性 */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            核心功能
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <FeatureCard
                key={index}
                feature={feature}
                delay={index * 100}
              />
            ))}
          </div>
        </div>

        {/* 系统规格 */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            系统规格
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickStats.map((stat, index) => (
              <div
                key={index}
                className="card p-6 text-center"
              >
                <div className="text-2xl font-bold text-primary-400 mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-300">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 使用流程 */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            使用流程
          </h2>
          <div className="max-w-4xl mx-auto">
            <WorkflowSteps onNavigate={onNavigate} />
          </div>
        </div>

        {/* 底部信息 */}
        <div className="text-center text-gray-400">
          <p className="mb-2">
            Attitude Tracker 3D - 专业的三维姿态可视化解决方案
          </p>
          <p className="text-sm">
            版本 1.0.0 | 基于 React + Three.js 构建
          </p>
        </div>
      </div>
    </div>
  );
};

/**
 * 功能卡片组件
 */
const FeatureCard = ({ feature, delay }) => {
  return (
    <div
      className="card p-8 text-center hover:bg-gray-700 transition-all duration-300 cursor-pointer transform hover:scale-105"
      onClick={feature.action}
      style={{ animationDelay: `${delay}ms` }}
    >
      <div className="text-4xl mb-4">{feature.icon}</div>
      <h3 className="text-xl font-semibold text-white mb-3">
        {feature.title}
      </h3>
      <p className="text-gray-300 mb-6">
        {feature.description}
      </p>
      <button className="btn-primary w-full">
        开始使用
      </button>
    </div>
  );
};

/**
 * 工作流程步骤组件
 */
const WorkflowSteps = ({ onNavigate }) => {
  const steps = [
    {
      number: 1,
      title: '输入数据',
      description: '手动输入或上传CSV文件',
      action: () => onNavigate('input')
    },
    {
      number: 2,
      title: '3D展示',
      description: '查看三维姿态可视化',
      action: () => onNavigate('viewer')
    },
    {
      number: 3,
      title: '播放控制',
      description: '控制动画播放和设置',
      action: () => onNavigate('viewer')
    },
    {
      number: 4,
      title: '保存记录',
      description: '保存到历史记录中',
      action: () => onNavigate('history')
    }
  ];

  return (
    <div className="flex flex-col md:flex-row items-center justify-between space-y-8 md:space-y-0 md:space-x-4">
      {steps.map((step, index) => (
        <React.Fragment key={step.number}>
          <div
            className="flex flex-col items-center text-center cursor-pointer group"
            onClick={step.action}
          >
            <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold text-xl mb-4 group-hover:bg-primary-500 transition-colors duration-200">
              {step.number}
            </div>
            <h3 className="text-lg font-semibold text-white mb-2 group-hover:text-primary-400 transition-colors duration-200">
              {step.title}
            </h3>
            <p className="text-gray-300 text-sm max-w-32">
              {step.description}
            </p>
          </div>
          {index < steps.length - 1 && (
            <div className="hidden md:block w-8 h-0.5 bg-gray-600"></div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default Home;
