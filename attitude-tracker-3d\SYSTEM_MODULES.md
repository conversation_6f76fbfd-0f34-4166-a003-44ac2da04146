# Attitude Tracker 3D 系统模块总结

## 🏗️ 系统架构概览

Attitude Tracker 3D 是一个基于Web的三维姿态追踪可视化系统，采用模块化架构设计，主要包含以下核心模块：

## 📋 核心模块列表

### 1. 🎯 **应用框架模块 (App Framework)**
- **App.jsx** - 应用主框架，路由管理和全局状态
- **main.jsx** - 应用入口点，React DOM渲染
- **index.css** - 全局样式和Tailwind CSS配置

### 2. 📄 **页面模块 (Pages)**
- **Home.jsx** - 系统首页，功能导航和介绍
- **DataInput.jsx** - 数据输入页面，支持手动输入、文件上传、实时预览
- **Viewer3D.jsx** - 3D可视化页面，Three.js场景渲染和动画播放
- **History.jsx** - 历史记录页面，数据管理和查看

### 3. 🧩 **组件模块 (Components)**
- **Header.jsx** - 顶部导航栏，页面切换和标题显示
- **Sidebar.jsx** - 侧边栏导航，设置和工具功能
- **ControlPanel.jsx** - 3D控制面板，播放控制和设置调整
- **SettingsModal.jsx** - 设置模态框，单位、显示、导出配置

### 4. 🔧 **服务模块 (Services)**
- **dataParser.js** - 数据解析服务
  - CSV文件解析
  - 手动输入数据验证
  - 角度单位转换
  - 数据格式标准化

### 5. 🗄️ **状态管理模块 (Store)**
- **dataStore.js** - 全局数据存储
  - 姿态数据管理
  - 实时预览状态
  - 订阅者模式实现
  - 数据持久化

### 6. 🛠️ **工具模块 (Utils)**
- **playback.js** - 播放控制器
  - 动画播放逻辑
  - 速度控制
  - 进度管理
  - 事件回调系统

## 🎨 **用户界面层次**

```
┌─────────────────────────────────────┐
│            Header (导航栏)            │
├─────────────┬───────────────────────┤
│             │                       │
│  Sidebar    │     Main Content      │
│  (侧边栏)    │     (主要内容)         │
│             │                       │
│  - 设置     │  ┌─ Home (首页)       │
│  - 工具     │  ├─ DataInput (输入)  │
│  - 导航     │  ├─ Viewer3D (3D)     │
│             │  └─ History (历史)    │
│             │                       │
└─────────────┴───────────────────────┘
```

## 🔄 **数据流架构**

```
用户输入 → DataParser → DataStore → Viewer3D → Three.js渲染
    ↓           ↓          ↓          ↓
  验证格式    角度转换    状态管理    播放控制
    ↓           ↓          ↓          ↓
  错误处理    单位统一    订阅通知    实时更新
```

## 📊 **功能模块详解**

### 🎯 **数据输入模块**
- **手动输入**: 表单输入姿态参数
- **文件上传**: CSV文件批量导入
- **实时预览**: 即时3D姿态预览
- **数据验证**: 格式检查和错误提示
- **示例数据**: 快速生成测试数据

### 🎮 **3D可视化模块**
- **场景渲染**: Three.js 3D场景管理
- **骨架模型**: 人体姿态可视化模型
- **动画播放**: 时间轴动画控制
- **交互控制**: 鼠标操作和视角控制
- **实时更新**: 数据变化即时反映

### 🎛️ **播放控制模块**
- **播放/暂停**: 动画播放状态控制
- **速度调节**: 0.25x - 2x播放速度
- **进度控制**: 拖拽跳转到指定时间点
- **循环播放**: 自动重复播放选项
- **帧精确**: 逐帧播放和精确控制

### ⚙️ **设置管理模块**
- **单位设置**: 角度(度/弧度)、长度、时间单位
- **显示设置**: 网格、坐标轴、轨迹显示开关
- **导出设置**: 文件格式、质量、元数据选项
- **主题设置**: 背景颜色、网格颜色自定义

### 🔧 **工具功能模块**
- **截图功能**: Canvas截图并下载PNG文件
- **录制功能**: 动画录制状态管理
- **重置功能**: 清除所有设置和数据
- **导出功能**: JSON/CSV格式数据导出

## 🏛️ **技术架构层次**

### **表现层 (Presentation Layer)**
- React组件
- Tailwind CSS样式
- 响应式设计

### **业务逻辑层 (Business Logic Layer)**
- 数据解析和验证
- 姿态计算和转换
- 播放控制逻辑

### **数据层 (Data Layer)**
- 内存状态管理
- 本地存储持久化
- 文件导入导出

### **渲染层 (Rendering Layer)**
- Three.js 3D引擎
- WebGL硬件加速
- 实时渲染管道

## 🔗 **模块依赖关系**

```
App.jsx
├── Pages/
│   ├── Home.jsx
│   ├── DataInput.jsx ──→ dataParser.js ──→ dataStore.js
│   ├── Viewer3D.jsx ──→ playback.js ──→ Three.js
│   └── History.jsx ──→ dataStore.js
├── Components/
│   ├── Header.jsx
│   ├── Sidebar.jsx ──→ SettingsModal.jsx
│   └── ControlPanel.jsx ──→ playback.js
└── Services/
    ├── dataParser.js
    ├── dataStore.js
    └── playback.js
```

## 📈 **性能优化模块**

### **渲染优化**
- Three.js场景优化
- 动画帧率控制
- 内存管理

### **数据处理优化**
- 大文件分块解析
- 数据缓存机制
- 异步处理

### **用户体验优化**
- 加载状态提示
- 错误处理机制
- 响应式交互

## 🧪 **测试模块**
- **功能测试**: 核心功能验证
- **性能测试**: 渲染性能评估
- **兼容性测试**: 浏览器兼容性
- **用户体验测试**: 交互流程验证

## 📦 **构建和部署模块**
- **Vite构建**: 快速开发和构建
- **热更新**: 开发时实时更新
- **代码分割**: 按需加载优化
- **静态资源**: 图片、字体等资源管理

---

**总结**: Attitude Tracker 3D 采用现代化的模块化架构，各模块职责清晰，耦合度低，易于维护和扩展。系统支持完整的姿态数据处理流程，从输入到可视化的全链路功能。
