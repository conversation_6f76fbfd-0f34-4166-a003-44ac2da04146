/**
 * 动画播放控制工具模块
 * 用途：管理姿态数据的播放、暂停、重置等操作
 */

/**
 * 播放控制器类
 */
export class PlaybackController {
  constructor() {
    this.poseData = [];
    this.currentIndex = 0;
    this.isPlaying = false;
    this.playbackSpeed = 1.0;
    this.animationId = null;
    this.lastTimestamp = 0;
    this.callbacks = {
      onUpdate: null,
      onComplete: null,
      onStart: null,
      onPause: null
    };
  }

  /**
   * 设置姿态数据
   * @param {Array} poseData - PosePoint数组
   */
  setData(poseData) {
    if (!Array.isArray(poseData)) {
      throw new Error('姿态数据必须是数组格式');
    }
    
    this.poseData = [...poseData];
    this.currentIndex = 0;
    this.lastTimestamp = 0;
  }

  /**
   * 开始播放
   */
  play() {
    if (this.poseData.length === 0) {
      throw new Error('没有可播放的数据');
    }

    if (this.isPlaying) {
      return;
    }

    this.isPlaying = true;
    this.lastTimestamp = performance.now();
    
    if (this.callbacks.onStart) {
      this.callbacks.onStart();
    }

    this.animate();
  }

  /**
   * 暂停播放
   */
  pause() {
    if (!this.isPlaying) {
      return;
    }

    this.isPlaying = false;
    
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }

    if (this.callbacks.onPause) {
      this.callbacks.onPause();
    }
  }

  /**
   * 重置播放
   */
  reset() {
    this.pause();
    this.currentIndex = 0;
    this.lastTimestamp = 0;
    
    if (this.poseData.length > 0 && this.callbacks.onUpdate) {
      this.callbacks.onUpdate(this.poseData[0], 0, this.poseData.length);
    }
  }

  /**
   * 跳转到指定位置
   * @param {number} index - 目标索引
   */
  seekTo(index) {
    if (index < 0 || index >= this.poseData.length) {
      throw new Error('索引超出范围');
    }

    this.currentIndex = index;
    
    if (this.callbacks.onUpdate) {
      this.callbacks.onUpdate(this.poseData[index], index, this.poseData.length);
    }
  }

  /**
   * 设置播放速度
   * @param {number} speed - 播放速度倍率
   */
  setSpeed(speed) {
    if (speed <= 0) {
      throw new Error('播放速度必须大于0');
    }
    
    this.playbackSpeed = speed;
  }

  /**
   * 设置回调函数
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (this.callbacks.hasOwnProperty(event)) {
      this.callbacks[event] = callback;
    } else {
      throw new Error(`不支持的事件类型: ${event}`);
    }
  }

  /**
   * 获取当前播放状态
   * @returns {Object} 播放状态信息
   */
  getStatus() {
    return {
      isPlaying: this.isPlaying,
      currentIndex: this.currentIndex,
      totalFrames: this.poseData.length,
      progress: this.poseData.length > 0 ? this.currentIndex / this.poseData.length : 0,
      playbackSpeed: this.playbackSpeed
    };
  }

  /**
   * 动画循环函数
   */
  animate() {
    if (!this.isPlaying) {
      return;
    }

    const currentTime = performance.now();
    const deltaTime = currentTime - this.lastTimestamp;
    
    // 根据播放速度调整时间间隔
    const frameInterval = 1000 / 30; // 30fps
    const adjustedInterval = frameInterval / this.playbackSpeed;

    if (deltaTime >= adjustedInterval) {
      this.updateFrame();
      this.lastTimestamp = currentTime;
    }

    this.animationId = requestAnimationFrame(() => this.animate());
  }

  /**
   * 更新当前帧
   */
  updateFrame() {
    if (this.currentIndex >= this.poseData.length) {
      this.pause();
      
      if (this.callbacks.onComplete) {
        this.callbacks.onComplete();
      }
      return;
    }

    const currentPose = this.poseData[this.currentIndex];
    
    if (this.callbacks.onUpdate) {
      this.callbacks.onUpdate(currentPose, this.currentIndex, this.poseData.length);
    }

    this.currentIndex++;
  }

  /**
   * 销毁播放控制器
   */
  destroy() {
    this.pause();
    this.poseData = [];
    this.callbacks = {};
  }
}

/**
 * 创建播放控制器实例
 * @returns {PlaybackController} 播放控制器实例
 */
export function createPlaybackController() {
  return new PlaybackController();
}
