<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边栏功能调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 侧边栏功能调试指南</h1>
        
        <div class="status info">
            <strong>📋 当前状态检查</strong><br>
            根据您的描述，设置和工具按钮没有响应。让我们逐步调试这个问题。
        </div>

        <h2>🎯 快速测试</h2>
        
        <div class="test-section">
            <h3>1. 打开应用并检查控制台</h3>
            <div class="step">
                <strong>步骤：</strong>
                <ol>
                    <li>打开应用: <a href="http://127.0.0.1:3001/" target="_blank" class="button">🚀 打开应用</a></li>
                    <li>按F12打开开发者工具</li>
                    <li>切换到Console标签页</li>
                    <li>点击侧边栏中的任意设置或工具按钮</li>
                    <li>观察控制台输出</li>
                </ol>
            </div>
            
            <div class="status warning">
                <strong>预期结果：</strong> 应该看到类似 "点击了菜单项: 单位设置" 的日志输出
            </div>
        </div>

        <div class="test-section">
            <h3>2. 检查侧边栏是否显示</h3>
            <div class="step">
                <strong>注意：</strong> 侧边栏只在特定页面显示：
                <ul>
                    <li>✅ 首页 - 有侧边栏</li>
                    <li>✅ 数据输入页 - 有侧边栏</li>
                    <li>❌ 3D展示页 - 无侧边栏</li>
                    <li>✅ 历史记录页 - 有侧边栏</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>3. 手动测试每个功能</h3>
            
            <h4>📐 设置功能测试</h4>
            <div class="step">
                <strong>单位设置：</strong>
                <ol>
                    <li>点击侧边栏"设置" → "单位设置"</li>
                    <li>应该打开设置模态框</li>
                    <li>可以修改角度、长度、时间单位</li>
                    <li>点击保存后设置应该持久化</li>
                </ol>
            </div>

            <div class="step">
                <strong>显示设置：</strong>
                <ol>
                    <li>点击侧边栏"设置" → "显示设置"</li>
                    <li>应该打开显示设置模态框</li>
                    <li>可以切换网格、坐标轴、轨迹显示</li>
                    <li>可以修改背景和网格颜色</li>
                </ol>
            </div>

            <div class="step">
                <strong>导出设置：</strong>
                <ol>
                    <li>点击侧边栏"设置" → "导出设置"</li>
                    <li>应该打开导出设置模态框</li>
                    <li>可以选择导出格式和质量</li>
                </ol>
            </div>

            <h4>🔧 工具功能测试</h4>
            <div class="step">
                <strong>截图功能：</strong>
                <ol>
                    <li>点击侧边栏"工具" → "截图"</li>
                    <li>应该自动下载一个PNG文件</li>
                    <li>文件名格式: attitude-tracker-screenshot-[时间戳].png</li>
                </ol>
            </div>

            <div class="step">
                <strong>录制功能：</strong>
                <ol>
                    <li>点击侧边栏"工具" → "录制"</li>
                    <li>应该显示"开始录制"提示</li>
                    <li>菜单项应该变为"🔴 录制中"</li>
                    <li>再次点击应该停止录制</li>
                </ol>
            </div>

            <div class="step">
                <strong>重置功能：</strong>
                <ol>
                    <li>点击侧边栏"工具" → "重置"</li>
                    <li>应该显示确认对话框</li>
                    <li>确认后清除所有设置</li>
                    <li>询问是否刷新页面</li>
                </ol>
            </div>
        </div>

        <h2>🐛 常见问题排查</h2>

        <div class="test-section">
            <h3>问题1: 按钮点击没有任何反应</h3>
            <div class="status error">
                <strong>可能原因：</strong>
                <ul>
                    <li>JavaScript错误阻止了事件处理</li>
                    <li>组件没有正确渲染</li>
                    <li>事件处理函数没有正确绑定</li>
                </ul>
            </div>
            
            <div class="step">
                <strong>解决方案：</strong>
                <ol>
                    <li>检查浏览器控制台是否有错误信息</li>
                    <li>确认在有侧边栏的页面进行测试</li>
                    <li>尝试刷新页面 (Ctrl+F5)</li>
                    <li>检查网络连接是否正常</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>问题2: 设置模态框不显示</h3>
            <div class="step">
                <strong>检查步骤：</strong>
                <ol>
                    <li>确认点击的是设置类按钮（单位设置、显示设置、导出设置）</li>
                    <li>检查控制台是否有React组件错误</li>
                    <li>确认SettingsModal组件正确导入</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>问题3: 工具功能不执行</h3>
            <div class="step">
                <strong>检查步骤：</strong>
                <ol>
                    <li>确认浏览器支持Canvas API（截图功能）</li>
                    <li>检查浏览器是否阻止了文件下载</li>
                    <li>确认localStorage功能正常</li>
                </ol>
            </div>
        </div>

        <h2>🔧 手动修复步骤</h2>

        <div class="test-section">
            <h3>如果功能仍然不工作，请尝试：</h3>
            
            <div class="step">
                <strong>1. 重启开发服务器</strong>
                <div class="code">
                    # 在命令行中执行：
                    Ctrl+C  # 停止当前服务器
                    npm run dev  # 重新启动
                </div>
            </div>

            <div class="step">
                <strong>2. 清除浏览器缓存</strong>
                <ol>
                    <li>按Ctrl+Shift+Delete</li>
                    <li>选择"缓存的图片和文件"</li>
                    <li>点击清除数据</li>
                    <li>刷新页面</li>
                </ol>
            </div>

            <div class="step">
                <strong>3. 检查浏览器兼容性</strong>
                <p>确保使用以下浏览器之一：</p>
                <ul>
                    <li>Chrome 88+</li>
                    <li>Firefox 85+</li>
                    <li>Safari 14+</li>
                    <li>Edge 88+</li>
                </ul>
            </div>
        </div>

        <h2>📞 获取帮助</h2>

        <div class="status info">
            <strong>如果问题仍然存在，请提供以下信息：</strong>
            <ol>
                <li>具体在哪个页面测试的（首页/数据输入/历史记录）</li>
                <li>点击了哪个具体的按钮</li>
                <li>浏览器控制台的错误信息（如果有）</li>
                <li>浏览器类型和版本</li>
                <li>是否看到了"点击了菜单项: xxx"的日志输出</li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="http://127.0.0.1:3001/" target="_blank" class="button" style="font-size: 18px; padding: 15px 30px;">
                🔍 开始调试测试
            </a>
        </div>
    </div>

    <script>
        // 检查服务器状态
        fetch('http://127.0.0.1:3001/')
            .then(response => {
                if (response.ok) {
                    console.log('✅ 开发服务器运行正常');
                    document.querySelector('.container').insertAdjacentHTML('afterbegin', 
                        '<div class="status success">✅ 开发服务器连接正常</div>'
                    );
                } else {
                    console.warn('⚠️ 开发服务器响应异常');
                    document.querySelector('.container').insertAdjacentHTML('afterbegin', 
                        '<div class="status warning">⚠️ 开发服务器响应异常</div>'
                    );
                }
            })
            .catch(error => {
                console.error('❌ 无法连接到开发服务器:', error);
                document.querySelector('.container').insertAdjacentHTML('afterbegin', 
                    '<div class="status error">❌ 无法连接到开发服务器，请确认服务器正在运行</div>'
                );
            });
    </script>
</body>
</html>
