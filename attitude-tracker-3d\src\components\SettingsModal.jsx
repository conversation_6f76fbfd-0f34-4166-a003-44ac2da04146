/**
 * 设置模态框组件
 * 用途：提供更好的设置界面体验
 */

import React, { useState, useEffect } from 'react';

const SettingsModal = ({ isOpen, onClose, settingType }) => {
  const [settings, setSettings] = useState({});

  useEffect(() => {
    if (isOpen) {
      loadSettings();
    }
  }, [isOpen, settingType]);

  const loadSettings = () => {
    switch (settingType) {
      case 'units':
        setSettings({
          angleUnit: localStorage.getItem('angleUnit') || 'degrees',
          lengthUnit: localStorage.getItem('lengthUnit') || 'meters',
          timeUnit: localStorage.getItem('timeUnit') || 'seconds'
        });
        break;
      case 'display':
        const displaySettings = JSON.parse(localStorage.getItem('displaySettings') || '{}');
        setSettings({
          showGrid: displaySettings.showGrid !== false,
          showAxes: displaySettings.showAxes !== false,
          showTrajectory: displaySettings.showTrajectory !== false,
          backgroundColor: displaySettings.backgroundColor || '#0f172a',
          gridColor: displaySettings.gridColor || '#444444'
        });
        break;
      case 'export':
        setSettings({
          format: localStorage.getItem('exportFormat') || 'JSON',
          quality: localStorage.getItem('exportQuality') || 'high',
          includeMetadata: localStorage.getItem('includeMetadata') !== 'false'
        });
        break;
      default:
        setSettings({});
    }
  };

  const saveSettings = () => {
    switch (settingType) {
      case 'units':
        localStorage.setItem('angleUnit', settings.angleUnit);
        localStorage.setItem('lengthUnit', settings.lengthUnit);
        localStorage.setItem('timeUnit', settings.timeUnit);
        break;
      case 'display':
        localStorage.setItem('displaySettings', JSON.stringify(settings));
        break;
      case 'export':
        localStorage.setItem('exportFormat', settings.format);
        localStorage.setItem('exportQuality', settings.quality);
        localStorage.setItem('includeMetadata', settings.includeMetadata.toString());
        break;
    }
    
    // 触发自定义事件通知其他组件设置已更新
    window.dispatchEvent(new CustomEvent('settingsUpdated', { detail: { type: settingType, settings } }));
    
    onClose();
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  if (!isOpen) return null;

  const renderUnitsSettings = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">角度单位</label>
        <select
          value={settings.angleUnit}
          onChange={(e) => handleSettingChange('angleUnit', e.target.value)}
          className="input-field w-full"
        >
          <option value="degrees">度 (°)</option>
          <option value="radians">弧度 (rad)</option>
        </select>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">长度单位</label>
        <select
          value={settings.lengthUnit}
          onChange={(e) => handleSettingChange('lengthUnit', e.target.value)}
          className="input-field w-full"
        >
          <option value="meters">米 (m)</option>
          <option value="centimeters">厘米 (cm)</option>
          <option value="millimeters">毫米 (mm)</option>
          <option value="feet">英尺 (ft)</option>
          <option value="inches">英寸 (in)</option>
        </select>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">时间单位</label>
        <select
          value={settings.timeUnit}
          onChange={(e) => handleSettingChange('timeUnit', e.target.value)}
          className="input-field w-full"
        >
          <option value="seconds">秒 (s)</option>
          <option value="milliseconds">毫秒 (ms)</option>
          <option value="minutes">分钟 (min)</option>
        </select>
      </div>
    </div>
  );

  const renderDisplaySettings = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={settings.showGrid}
            onChange={(e) => handleSettingChange('showGrid', e.target.checked)}
            className="rounded"
          />
          <span className="text-sm text-white">显示网格</span>
        </label>
        
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={settings.showAxes}
            onChange={(e) => handleSettingChange('showAxes', e.target.checked)}
            className="rounded"
          />
          <span className="text-sm text-white">显示坐标轴</span>
        </label>
        
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={settings.showTrajectory}
            onChange={(e) => handleSettingChange('showTrajectory', e.target.checked)}
            className="rounded"
          />
          <span className="text-sm text-white">显示轨迹</span>
        </label>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">背景颜色</label>
        <input
          type="color"
          value={settings.backgroundColor}
          onChange={(e) => handleSettingChange('backgroundColor', e.target.value)}
          className="w-full h-10 rounded border border-gray-600"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">网格颜色</label>
        <input
          type="color"
          value={settings.gridColor}
          onChange={(e) => handleSettingChange('gridColor', e.target.value)}
          className="w-full h-10 rounded border border-gray-600"
        />
      </div>
    </div>
  );

  const renderExportSettings = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">导出格式</label>
        <select
          value={settings.format}
          onChange={(e) => handleSettingChange('format', e.target.value)}
          className="input-field w-full"
        >
          <option value="JSON">JSON</option>
          <option value="CSV">CSV</option>
          <option value="PNG">PNG 图片</option>
          <option value="MP4">MP4 视频</option>
        </select>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">导出质量</label>
        <select
          value={settings.quality}
          onChange={(e) => handleSettingChange('quality', e.target.value)}
          className="input-field w-full"
        >
          <option value="low">低质量</option>
          <option value="medium">中等质量</option>
          <option value="high">高质量</option>
          <option value="ultra">超高质量</option>
        </select>
      </div>
      
      <label className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={settings.includeMetadata}
          onChange={(e) => handleSettingChange('includeMetadata', e.target.checked)}
          className="rounded"
        />
        <span className="text-sm text-white">包含元数据</span>
      </label>
    </div>
  );

  const getTitle = () => {
    switch (settingType) {
      case 'units': return '单位设置';
      case 'display': return '显示设置';
      case 'export': return '导出设置';
      default: return '设置';
    }
  };

  const renderContent = () => {
    switch (settingType) {
      case 'units': return renderUnitsSettings();
      case 'display': return renderDisplaySettings();
      case 'export': return renderExportSettings();
      default: return <div>未知设置类型</div>;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg max-w-md w-full max-h-[80vh] overflow-y-auto">
        <div className="p-6">
          {/* 模态框头部 */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-white flex items-center">
              <span className="mr-2">⚙️</span>
              {getTitle()}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white text-2xl"
            >
              ✕
            </button>
          </div>

          {/* 设置内容 */}
          <div className="mb-6">
            {renderContent()}
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={saveSettings}
              className="btn-primary flex-1"
            >
              保存设置
            </button>
            <button
              onClick={onClose}
              className="btn-secondary"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
