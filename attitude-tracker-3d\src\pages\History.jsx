/**
 * 历史记录页面组件
 * 用途：显示和管理历史姿态数据记录
 */

import React, { useState, useEffect } from 'react';
import { setData } from '../store/dataStore.js';

const History = ({ onNavigate }) => {
  const [historyRecords, setHistoryRecords] = useState([]);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('date'); // 'date' | 'name' | 'size'
  const [sortOrder, setSortOrder] = useState('desc'); // 'asc' | 'desc'

  // 模拟历史记录数据
  useEffect(() => {
    const mockRecords = generateMockRecords();
    setHistoryRecords(mockRecords);
  }, []);

  // 生成模拟历史记录
  const generateMockRecords = () => {
    const records = [];
    const now = Date.now();
    
    for (let i = 0; i < 15; i++) {
      records.push({
        id: `record_${i}`,
        name: `姿态记录 ${i + 1}`,
        description: `第${i + 1}次姿态数据记录`,
        createdAt: now - i * 24 * 60 * 60 * 1000, // 每天一个记录
        dataPoints: Math.floor(Math.random() * 500) + 50,
        duration: Math.floor(Math.random() * 300) + 30, // 30-330秒
        tags: ['测试', '演示', '实验'].slice(0, Math.floor(Math.random() * 3) + 1),
        thumbnail: generateThumbnail(),
        data: generateSampleData(Math.floor(Math.random() * 100) + 20)
      });
    }
    
    return records;
  };

  // 生成缩略图数据
  const generateThumbnail = () => {
    const points = [];
    for (let i = 0; i < 20; i++) {
      points.push({
        x: i * 5,
        y: Math.sin(i * 0.3) * 20 + 50
      });
    }
    return points;
  };

  // 生成示例数据
  const generateSampleData = (count) => {
    const data = [];
    const startTime = Date.now();
    
    for (let i = 0; i < count; i++) {
      data.push({
        timestamp: startTime + i * 100,
        pitch: Math.sin(i * 0.1) * 30,
        yaw: Math.cos(i * 0.1) * 45,
        roll: Math.sin(i * 0.05) * 15,
        x: Math.sin(i * 0.08) * 2,
        y: Math.cos(i * 0.06) * 1.5,
        z: i * 0.01
      });
    }
    
    return data;
  };

  // 过滤和排序记录
  const filteredAndSortedRecords = historyRecords
    .filter(record => 
      record.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'size':
          aValue = a.dataPoints;
          bValue = b.dataPoints;
          break;
        case 'date':
        default:
          aValue = a.createdAt;
          bValue = b.createdAt;
          break;
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

  // 加载记录到3D视图
  const loadRecord = (record) => {
    setData(record.data);
    onNavigate('viewer');
  };

  // 删除记录
  const deleteRecord = (recordId) => {
    if (confirm('确定要删除这条记录吗？')) {
      setHistoryRecords(prev => prev.filter(r => r.id !== recordId));
      if (selectedRecord && selectedRecord.id === recordId) {
        setSelectedRecord(null);
      }
    }
  };

  // 导出记录
  const exportRecord = (record) => {
    const dataStr = JSON.stringify(record.data, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.download = `${record.name}-${Date.now()}.json`;
    link.href = url;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="container mx-auto px-6">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-4">
            历史记录
          </h1>
          <p className="text-gray-300">
            管理和回放历史姿态数据记录
          </p>
        </div>

        {/* 搜索和排序控制 */}
        <div className="max-w-6xl mx-auto mb-8">
          <div className="card p-6">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
              {/* 搜索框 */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="搜索记录..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input-field w-full pl-10"
                  />
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                    🔍
                  </span>
                </div>
              </div>

              {/* 排序控制 */}
              <div className="flex items-center space-x-4">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="input-field"
                >
                  <option value="date">按日期排序</option>
                  <option value="name">按名称排序</option>
                  <option value="size">按大小排序</option>
                </select>
                
                <button
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="btn-secondary"
                >
                  {sortOrder === 'asc' ? '↑' : '↓'}
                </button>
              </div>
            </div>

            {/* 统计信息 */}
            <div className="mt-4 pt-4 border-t border-gray-700">
              <div className="flex items-center justify-between text-sm text-gray-400">
                <span>共 {filteredAndSortedRecords.length} 条记录</span>
                <span>
                  总数据点: {filteredAndSortedRecords.reduce((sum, r) => sum + r.dataPoints, 0)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 记录列表 */}
        <div className="max-w-6xl mx-auto">
          {filteredAndSortedRecords.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📋</div>
              <h2 className="text-2xl font-bold text-white mb-2">暂无记录</h2>
              <p className="text-gray-400 mb-6">
                {searchTerm ? '没有找到匹配的记录' : '还没有保存任何姿态数据记录'}
              </p>
              <button
                onClick={() => onNavigate('input')}
                className="btn-primary"
              >
                创建新记录
              </button>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredAndSortedRecords.map((record) => (
                <RecordCard
                  key={record.id}
                  record={record}
                  onLoad={() => loadRecord(record)}
                  onDelete={() => deleteRecord(record.id)}
                  onExport={() => exportRecord(record)}
                  onSelect={() => setSelectedRecord(record)}
                  isSelected={selectedRecord && selectedRecord.id === record.id}
                />
              ))}
            </div>
          )}
        </div>

        {/* 记录详情模态框 */}
        {selectedRecord && (
          <RecordDetailModal
            record={selectedRecord}
            onClose={() => setSelectedRecord(null)}
            onLoad={() => loadRecord(selectedRecord)}
          />
        )}
      </div>
    </div>
  );
};

/**
 * 记录卡片组件
 */
const RecordCard = ({ record, onLoad, onDelete, onExport, onSelect, isSelected }) => {
  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`card p-6 cursor-pointer transition-all duration-200 hover:bg-gray-700 ${
      isSelected ? 'ring-2 ring-primary-500' : ''
    }`}>
      {/* 缩略图 */}
      <div className="h-32 bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
        <ThumbnailChart data={record.thumbnail} />
      </div>

      {/* 记录信息 */}
      <div className="space-y-3">
        <div>
          <h3 className="text-lg font-semibold text-white mb-1">
            {record.name}
          </h3>
          <p className="text-sm text-gray-400">
            {record.description}
          </p>
        </div>

        {/* 标签 */}
        <div className="flex flex-wrap gap-1">
          {record.tags.map((tag, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-primary-600 text-white text-xs rounded"
            >
              {tag}
            </span>
          ))}
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-2 gap-2 text-sm text-gray-400">
          <div>📊 {record.dataPoints} 点</div>
          <div>⏱️ {formatDuration(record.duration)}</div>
          <div className="col-span-2">📅 {formatDate(record.createdAt)}</div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-2">
          <button
            onClick={(e) => { e.stopPropagation(); onLoad(); }}
            className="btn-primary flex-1 text-sm py-2"
          >
            加载
          </button>
          <button
            onClick={(e) => { e.stopPropagation(); onSelect(); }}
            className="btn-secondary text-sm py-2 px-3"
          >
            详情
          </button>
          <button
            onClick={(e) => { e.stopPropagation(); onExport(); }}
            className="btn-secondary text-sm py-2 px-3"
          >
            💾
          </button>
          <button
            onClick={(e) => { e.stopPropagation(); onDelete(); }}
            className="bg-red-600 hover:bg-red-700 text-white text-sm py-2 px-3 rounded"
          >
            🗑️
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * 缩略图图表组件
 */
const ThumbnailChart = ({ data }) => {
  const maxY = Math.max(...data.map(p => p.y));
  const minY = Math.min(...data.map(p => p.y));
  const range = maxY - minY || 1;

  return (
    <svg width="100%" height="100%" viewBox="0 0 100 60" className="text-primary-400">
      <polyline
        fill="none"
        stroke="currentColor"
        strokeWidth="1"
        points={data.map((point, index) => 
          `${(index / (data.length - 1)) * 100},${60 - ((point.y - minY) / range) * 60}`
        ).join(' ')}
      />
    </svg>
  );
};

/**
 * 记录详情模态框组件
 */
const RecordDetailModal = ({ record, onClose, onLoad }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
        <div className="p-6">
          {/* 模态框头部 */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-white">{record.name}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white text-2xl"
            >
              ✕
            </button>
          </div>

          {/* 详细信息 */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">描述</h3>
              <p className="text-gray-300">{record.description}</p>
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">统计信息</h3>
                <div className="space-y-2 text-sm text-gray-300">
                  <div>数据点数: {record.dataPoints}</div>
                  <div>持续时间: {Math.floor(record.duration / 60)}:{(record.duration % 60).toString().padStart(2, '0')}</div>
                  <div>创建时间: {new Date(record.createdAt).toLocaleString('zh-CN')}</div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-2">标签</h3>
                <div className="flex flex-wrap gap-2">
                  {record.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-primary-600 text-white text-sm rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* 数据预览 */}
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">数据预览</h3>
              <div className="bg-gray-700 rounded-lg p-4 h-40">
                <ThumbnailChart data={record.thumbnail} />
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-4 pt-4">
              <button
                onClick={onLoad}
                className="btn-primary flex-1"
              >
                加载到3D视图
              </button>
              <button
                onClick={onClose}
                className="btn-secondary"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default History;
