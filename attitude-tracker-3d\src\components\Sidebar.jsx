/**
 * 侧边栏组件
 * 用途：提供功能模块快速访问和设置面板
 */

import React, { useState } from 'react';
import SettingsModal from './SettingsModal.jsx';

const Sidebar = ({ isOpen, onToggle, currentPage, onNavigate }) => {
  const [activeSection, setActiveSection] = useState('navigation');
  const [settingsModal, setSettingsModal] = useState({ isOpen: false, type: null });

  const menuSections = [
    {
      id: 'navigation',
      title: '导航',
      icon: '🧭',
      items: [
        { id: 'home', label: '首页', icon: '🏠' },
        { id: 'input', label: '数据输入', icon: '📊' },
        { id: 'viewer', label: '3D展示', icon: '🎯' },
        { id: 'history', label: '历史记录', icon: '📋' }
      ]
    },
    {
      id: 'settings',
      title: '设置',
      icon: '⚙️',
      items: [
        { id: 'units', label: '单位设置', icon: '📐' },
        { id: 'display', label: '显示设置', icon: '🎨' },
        { id: 'export', label: '导出设置', icon: '💾' }
      ]
    },
    {
      id: 'tools',
      title: '工具',
      icon: '🔧',
      items: [
        { id: 'screenshot', label: '截图', icon: '📸' },
        { id: 'record', label: '录制', icon: '🎥' },
        { id: 'reset', label: '重置', icon: '🔄' }
      ]
    }
  ];

  return (
    <>
      {/* 侧边栏遮罩 */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* 侧边栏主体 */}
      <aside className={`
        fixed left-0 top-0 h-full bg-gray-900 border-r border-gray-700 z-50 transition-transform duration-300
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:z-auto
        w-64
      `}>
        {/* 侧边栏头部 */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">控制面板</h2>
            <button
              onClick={onToggle}
              className="lg:hidden text-gray-400 hover:text-white"
            >
              ✕
            </button>
          </div>
        </div>

        {/* 侧边栏内容 */}
        <div className="flex-1 overflow-y-auto">
          {menuSections.map((section) => (
            <MenuSection
              key={section.id}
              section={section}
              isActive={activeSection === section.id}
              onToggle={() => setActiveSection(
                activeSection === section.id ? null : section.id
              )}
              currentPage={currentPage}
              onNavigate={onNavigate}
              onOpenSettings={(type) => setSettingsModal({ isOpen: true, type })}
            />
          ))}
        </div>

        {/* 侧边栏底部 */}
        <div className="p-4 border-t border-gray-700">
          <div className="text-xs text-gray-500 text-center">
            <p>Attitude Tracker 3D</p>
            <p>v1.0.0</p>
          </div>
        </div>
      </aside>

      {/* 设置模态框 */}
      <SettingsModal
        isOpen={settingsModal.isOpen}
        settingType={settingsModal.type}
        onClose={() => setSettingsModal({ isOpen: false, type: null })}
      />
    </>
  );
};

/**
 * 菜单分组组件
 */
const MenuSection = ({ section, isActive, onToggle, currentPage, onNavigate, onOpenSettings }) => {
  return (
    <div className="border-b border-gray-800">
      {/* 分组标题 */}
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-800 transition-colors duration-200"
      >
        <div className="flex items-center space-x-3">
          <span className="text-lg">{section.icon}</span>
          <span className="font-medium text-white">{section.title}</span>
        </div>
        <span className={`text-gray-400 transition-transform duration-200 ${
          isActive ? 'rotate-180' : ''
        }`}>
          ▼
        </span>
      </button>

      {/* 分组内容 */}
      {isActive && (
        <div className="bg-gray-800">
          {section.items.map((item) => (
            <MenuItem
              key={item.id}
              item={item}
              isActive={currentPage === item.id}
              onNavigate={onNavigate}
              onOpenSettings={onOpenSettings}
            />
          ))}
        </div>
      )}
    </div>
  );
};

/**
 * 菜单项组件
 */
const MenuItem = ({ item, isActive, onNavigate, onOpenSettings }) => {
  const handleClick = () => {
    console.log(`点击了菜单项: ${item.label}`);

    // 根据菜单项ID进行相应的操作
    if (item.id) {
      // 页面导航类菜单项
      const pageMapping = {
        'home': 'home',
        'input': 'input',
        'viewer': 'viewer',
        'history': 'history'
      };

      const pageId = pageMapping[item.id];
      if (pageId && onNavigate) {
        onNavigate(pageId);
        return;
      }

      // 设置类菜单项 - 打开设置模态框
      if (item.id === 'units' || item.id === 'display' || item.id === 'export') {
        if (onOpenSettings) {
          onOpenSettings(item.id);
        }
      }
      // 工具类菜单项
      else if (item.id === 'screenshot') {
        handleScreenshot();
      } else if (item.id === 'record') {
        handleRecord();
      } else if (item.id === 'reset') {
        handleReset();
      }
    }
  };



  // 工具类功能处理函数
  const handleScreenshot = () => {
    console.log('执行截图功能');

    // 模拟截图功能
    const canvas = document.createElement('canvas');
    canvas.width = 800;
    canvas.height = 600;
    const ctx = canvas.getContext('2d');

    // 绘制简单的截图内容
    ctx.fillStyle = '#0f172a';
    ctx.fillRect(0, 0, 800, 600);
    ctx.fillStyle = '#3b82f6';
    ctx.font = '24px Arial';
    ctx.fillText('Attitude Tracker 3D - 截图', 250, 300);
    ctx.fillStyle = '#ffffff';
    ctx.font = '16px Arial';
    ctx.fillText(`截图时间: ${new Date().toLocaleString()}`, 280, 350);

    // 下载截图
    canvas.toBlob((blob) => {
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.download = `attitude-tracker-screenshot-${Date.now()}.png`;
      link.href = url;
      link.click();
      URL.revokeObjectURL(url);
    });

    alert('截图已保存到下载文件夹！');
  };

  const handleRecord = () => {
    const isRecording = localStorage.getItem('isRecording') === 'true';

    if (isRecording) {
      localStorage.setItem('isRecording', 'false');
      console.log('停止录制');
      alert('录制已停止！');
    } else {
      localStorage.setItem('isRecording', 'true');
      localStorage.setItem('recordStartTime', Date.now().toString());
      console.log('开始录制');
      alert('开始录制！点击再次停止录制。');
    }
  };

  const handleReset = () => {
    if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
      // 清除本地存储的设置
      localStorage.removeItem('angleUnit');
      localStorage.removeItem('displaySettings');
      localStorage.removeItem('exportFormat');
      localStorage.removeItem('isRecording');
      localStorage.removeItem('recordStartTime');

      console.log('所有设置已重置');
      alert('所有设置已重置为默认值！');

      // 刷新页面以应用默认设置
      if (confirm('是否刷新页面以应用默认设置？')) {
        window.location.reload();
      }
    }
  };

  // 获取菜单项的状态信息
  const getItemStatus = () => {
    if (item.id === 'record') {
      const isRecording = localStorage.getItem('isRecording') === 'true';
      return isRecording ? '🔴 录制中' : item.label;
    }
    if (item.id === 'units') {
      const unit = localStorage.getItem('angleUnit') || 'degrees';
      return `${item.label} (${unit === 'degrees' ? '度' : '弧度'})`;
    }
    return item.label;
  };

  const getItemStyle = () => {
    // 录制状态的特殊样式
    if (item.id === 'record' && localStorage.getItem('isRecording') === 'true') {
      return 'text-red-400 hover:text-red-300';
    }

    // 普通样式
    return isActive
      ? 'bg-primary-600 text-white'
      : 'text-gray-300 hover:bg-gray-700 hover:text-white';
  };

  return (
    <button
      onClick={handleClick}
      className={`
        w-full flex items-center justify-between px-6 py-3 text-left transition-colors duration-200
        ${getItemStyle()}
      `}
      title={`点击${item.label}`}
    >
      <div className="flex items-center space-x-3">
        <span className="text-base">{item.icon}</span>
        <span className="text-sm">{getItemStatus()}</span>
      </div>

      {/* 状态指示器 */}
      {item.id === 'record' && localStorage.getItem('isRecording') === 'true' && (
        <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></span>
      )}
    </button>
  );
};

export default Sidebar;
