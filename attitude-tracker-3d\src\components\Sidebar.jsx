/**
 * 侧边栏组件
 * 用途：提供功能模块快速访问和设置面板
 */

import React, { useState } from 'react';

const Sidebar = ({ isOpen, onToggle, currentPage }) => {
  const [activeSection, setActiveSection] = useState('navigation');

  const menuSections = [
    {
      id: 'navigation',
      title: '导航',
      icon: '🧭',
      items: [
        { id: 'home', label: '首页', icon: '🏠' },
        { id: 'input', label: '数据输入', icon: '📊' },
        { id: 'viewer', label: '3D展示', icon: '🎯' },
        { id: 'history', label: '历史记录', icon: '📋' }
      ]
    },
    {
      id: 'settings',
      title: '设置',
      icon: '⚙️',
      items: [
        { id: 'units', label: '单位设置', icon: '📐' },
        { id: 'display', label: '显示设置', icon: '🎨' },
        { id: 'export', label: '导出设置', icon: '💾' }
      ]
    },
    {
      id: 'tools',
      title: '工具',
      icon: '🔧',
      items: [
        { id: 'screenshot', label: '截图', icon: '📸' },
        { id: 'record', label: '录制', icon: '🎥' },
        { id: 'reset', label: '重置', icon: '🔄' }
      ]
    }
  ];

  return (
    <>
      {/* 侧边栏遮罩 */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* 侧边栏主体 */}
      <aside className={`
        fixed left-0 top-0 h-full bg-gray-900 border-r border-gray-700 z-50 transition-transform duration-300
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:z-auto
        w-64
      `}>
        {/* 侧边栏头部 */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">控制面板</h2>
            <button
              onClick={onToggle}
              className="lg:hidden text-gray-400 hover:text-white"
            >
              ✕
            </button>
          </div>
        </div>

        {/* 侧边栏内容 */}
        <div className="flex-1 overflow-y-auto">
          {menuSections.map((section) => (
            <MenuSection
              key={section.id}
              section={section}
              isActive={activeSection === section.id}
              onToggle={() => setActiveSection(
                activeSection === section.id ? null : section.id
              )}
              currentPage={currentPage}
            />
          ))}
        </div>

        {/* 侧边栏底部 */}
        <div className="p-4 border-t border-gray-700">
          <div className="text-xs text-gray-500 text-center">
            <p>Attitude Tracker 3D</p>
            <p>v1.0.0</p>
          </div>
        </div>
      </aside>
    </>
  );
};

/**
 * 菜单分组组件
 */
const MenuSection = ({ section, isActive, onToggle, currentPage }) => {
  return (
    <div className="border-b border-gray-800">
      {/* 分组标题 */}
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-800 transition-colors duration-200"
      >
        <div className="flex items-center space-x-3">
          <span className="text-lg">{section.icon}</span>
          <span className="font-medium text-white">{section.title}</span>
        </div>
        <span className={`text-gray-400 transition-transform duration-200 ${
          isActive ? 'rotate-180' : ''
        }`}>
          ▼
        </span>
      </button>

      {/* 分组内容 */}
      {isActive && (
        <div className="bg-gray-800">
          {section.items.map((item) => (
            <MenuItem
              key={item.id}
              item={item}
              isActive={currentPage === item.id}
            />
          ))}
        </div>
      )}
    </div>
  );
};

/**
 * 菜单项组件
 */
const MenuItem = ({ item, isActive }) => {
  const handleClick = () => {
    // 这里可以添加具体的功能逻辑
    console.log(`点击了菜单项: ${item.label}`);
  };

  return (
    <button
      onClick={handleClick}
      className={`
        w-full flex items-center space-x-3 px-6 py-3 text-left transition-colors duration-200
        ${isActive
          ? 'bg-primary-600 text-white'
          : 'text-gray-300 hover:bg-gray-700 hover:text-white'
        }
      `}
    >
      <span className="text-base">{item.icon}</span>
      <span className="text-sm">{item.label}</span>
    </button>
  );
};

export default Sidebar;
