/**
 * 数据输入页面组件
 * 用途：用户输入姿态数据或上传CSV文件
 */

import React, { useState, useRef } from 'react';
import { parseCSV, parseManualInput } from '../services/dataParser.js';
import { setData } from '../store/dataStore.js';

const DataInput = ({ onNavigate }) => {
  const [inputMode, setInputMode] = useState('manual'); // 'manual' | 'file'
  const [manualData, setManualData] = useState({
    timestamp: Date.now(),
    pitch: 0,
    yaw: 0,
    roll: 0,
    x: 0,
    y: 0,
    z: 0
  });
  const [angleUnit, setAngleUnit] = useState('degrees');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const fileInputRef = useRef(null);

  // 处理手动输入数据变更
  const handleManualInputChange = (field, value) => {
    setManualData(prev => ({
      ...prev,
      [field]: parseFloat(value) || 0
    }));
    setError('');
  };

  // 处理手动输入提交
  const handleManualSubmit = async () => {
    setIsLoading(true);
    setError('');
    
    try {
      const posePoint = parseManualInput(manualData);
      setData([posePoint]);
      setSuccess('数据已成功加载！');
      
      // 2秒后跳转到3D展示页面
      setTimeout(() => {
        onNavigate('viewer');
      }, 2000);
      
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理文件上传
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setIsLoading(true);
    setError('');

    try {
      const fileContent = await readFileContent(file);
      const poseData = parseCSV(fileContent);
      
      setData(poseData);
      setSuccess(`成功加载 ${poseData.length} 个数据点！`);
      
      // 2秒后跳转到3D展示页面
      setTimeout(() => {
        onNavigate('viewer');
      }, 2000);
      
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  // 读取文件内容
  const readFileContent = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = () => reject(new Error('文件读取失败'));
      reader.readAsText(file);
    });
  };

  // 生成示例数据
  const generateSampleData = () => {
    const sampleData = [];
    const startTime = Date.now();
    
    for (let i = 0; i < 100; i++) {
      sampleData.push({
        timestamp: startTime + i * 100,
        pitch: Math.sin(i * 0.1) * 30,
        yaw: Math.cos(i * 0.1) * 45,
        roll: Math.sin(i * 0.05) * 15,
        x: Math.sin(i * 0.08) * 2,
        y: Math.cos(i * 0.06) * 1.5,
        z: i * 0.01
      });
    }
    
    setData(sampleData);
    setSuccess(`生成了 ${sampleData.length} 个示例数据点！`);
    
    setTimeout(() => {
      onNavigate('viewer');
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="container mx-auto px-6">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-4">
            数据输入
          </h1>
          <p className="text-gray-300">
            输入姿态数据或上传CSV文件开始可视化
          </p>
        </div>

        {/* 输入模式选择 */}
        <div className="max-w-4xl mx-auto mb-8">
          <div className="flex justify-center space-x-4 mb-6">
            <button
              onClick={() => setInputMode('manual')}
              className={`px-6 py-3 rounded-lg font-medium transition-colors duration-200 ${
                inputMode === 'manual'
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              手动输入
            </button>
            <button
              onClick={() => setInputMode('file')}
              className={`px-6 py-3 rounded-lg font-medium transition-colors duration-200 ${
                inputMode === 'file'
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              文件上传
            </button>
          </div>

          {/* 输入内容区域 */}
          <div className="card p-8">
            {inputMode === 'manual' ? (
              <ManualInputForm
                data={manualData}
                angleUnit={angleUnit}
                onDataChange={handleManualInputChange}
                onAngleUnitChange={setAngleUnit}
                onSubmit={handleManualSubmit}
                isLoading={isLoading}
              />
            ) : (
              <FileUploadForm
                onFileUpload={handleFileUpload}
                fileInputRef={fileInputRef}
                isLoading={isLoading}
              />
            )}
          </div>

          {/* 示例数据按钮 */}
          <div className="text-center mt-6">
            <button
              onClick={generateSampleData}
              className="btn-secondary flex items-center space-x-2 mx-auto"
              disabled={isLoading}
            >
              <span>🎲</span>
              <span>生成示例数据</span>
            </button>
          </div>

          {/* 状态消息 */}
          {error && (
            <div className="mt-6 p-4 bg-red-900 border border-red-700 rounded-lg text-red-300">
              ❌ {error}
            </div>
          )}
          
          {success && (
            <div className="mt-6 p-4 bg-green-900 border border-green-700 rounded-lg text-green-300">
              ✅ {success}
            </div>
          )}
        </div>

        {/* CSV格式说明 */}
        <div className="max-w-4xl mx-auto">
          <CSVFormatGuide />
        </div>
      </div>
    </div>
  );
};

/**
 * 手动输入表单组件
 */
const ManualInputForm = ({ 
  data, 
  angleUnit, 
  onDataChange, 
  onAngleUnitChange, 
  onSubmit, 
  isLoading 
}) => {
  const inputFields = [
    { key: 'pitch', label: '俯仰角 (Pitch)', unit: angleUnit === 'degrees' ? '°' : 'rad' },
    { key: 'yaw', label: '偏航角 (Yaw)', unit: angleUnit === 'degrees' ? '°' : 'rad' },
    { key: 'roll', label: '翻滚角 (Roll)', unit: angleUnit === 'degrees' ? '°' : 'rad' },
    { key: 'x', label: 'X坐标', unit: 'm' },
    { key: 'y', label: 'Y坐标', unit: 'm' },
    { key: 'z', label: 'Z坐标', unit: 'm' }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold text-white">手动输入姿态数据</h3>
        <select
          value={angleUnit}
          onChange={(e) => onAngleUnitChange(e.target.value)}
          className="input-field"
        >
          <option value="degrees">角度 (°)</option>
          <option value="radians">弧度 (rad)</option>
        </select>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        {inputFields.map((field) => (
          <div key={field.key} className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">
              {field.label}
            </label>
            <div className="relative">
              <input
                type="number"
                step="0.01"
                value={data[field.key]}
                onChange={(e) => onDataChange(field.key, e.target.value)}
                className="input-field w-full pr-12"
                placeholder="0.00"
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
                {field.unit}
              </span>
            </div>
          </div>
        ))}
      </div>

      <button
        onClick={onSubmit}
        disabled={isLoading}
        className="btn-primary w-full flex items-center justify-center space-x-2"
      >
        {isLoading ? (
          <>
            <span className="animate-spin">⏳</span>
            <span>处理中...</span>
          </>
        ) : (
          <>
            <span>📊</span>
            <span>加载数据</span>
          </>
        )}
      </button>
    </div>
  );
};

/**
 * 文件上传表单组件
 */
const FileUploadForm = ({ onFileUpload, fileInputRef, isLoading }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-white">上传CSV文件</h3>
      
      <div className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center">
        <div className="space-y-4">
          <div className="text-4xl">📁</div>
          <div>
            <p className="text-lg text-white mb-2">选择CSV文件上传</p>
            <p className="text-gray-400 text-sm">
              支持包含timestamp, pitch, yaw, roll, x, y, z列的CSV文件
            </p>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept=".csv"
            onChange={onFileUpload}
            disabled={isLoading}
            className="hidden"
          />
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={isLoading}
            className="btn-primary"
          >
            {isLoading ? '处理中...' : '选择文件'}
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * CSV格式说明组件
 */
const CSVFormatGuide = () => {
  return (
    <div className="card p-6">
      <h3 className="text-lg font-semibold text-white mb-4">CSV文件格式说明</h3>
      <div className="space-y-4 text-sm text-gray-300">
        <p>CSV文件应包含以下列（顺序必须一致）：</p>
        <div className="bg-gray-800 p-4 rounded font-mono text-xs overflow-x-auto">
          timestamp,pitch,yaw,roll,x,y,z<br/>
          1640995200000,10.5,-5.2,2.1,0.5,1.2,0.8<br/>
          1640995200100,11.2,-4.8,2.3,0.6,1.3,0.9
        </div>
        <ul className="list-disc list-inside space-y-1">
          <li><strong>timestamp</strong>: 时间戳（毫秒）</li>
          <li><strong>pitch</strong>: 俯仰角（度或弧度）</li>
          <li><strong>yaw</strong>: 偏航角（度或弧度）</li>
          <li><strong>roll</strong>: 翻滚角（度或弧度）</li>
          <li><strong>x, y, z</strong>: 三维坐标（米）</li>
        </ul>
      </div>
    </div>
  );
};

export default DataInput;
