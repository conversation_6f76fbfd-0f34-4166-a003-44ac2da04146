# Attitude Tracker 3D - 代码架构文档

## 📋 项目概述

Attitude Tracker 3D是一个基于React + Three.js的Web应用，用于三维姿态数据的可视化、播放和管理。项目采用模块化架构，清晰分离了数据层、业务逻辑层和表现层。

## 🏗️ 项目结构图

```mermaid
graph TD
    A[attitude-tracker-3d/] --> B[src/]
    A --> C[public/]
    A --> D[tests/]
    A --> E[配置文件]
    
    B --> F[main.jsx]
    B --> G[App.jsx]
    B --> H[index.css]
    B --> I[components/]
    B --> J[pages/]
    B --> K[services/]
    B --> L[store/]
    B --> M[utils/]
    B --> N[assets/]
    B --> O[models/]
    
    I --> I1[Header.jsx]
    I --> I2[Sidebar.jsx]
    I --> I3[ControlPanel.jsx]
    I --> I4[SettingsModal.jsx]
    
    J --> J1[Home.jsx]
    J --> J2[DataInput.jsx]
    J --> J3[Viewer3D.jsx]
    J --> J4[History.jsx]
    
    K --> K1[dataParser.js]
    
    L --> L1[dataStore.js]
    
    M --> M1[playback.js]
    
    C --> C1[index.html]
    
    D --> D1[测试文件集合]
    
    E --> E1[package.json]
    E --> E2[vite.config.js]
    E --> E3[tailwind.config.js]
    E --> E4[README.md]
```

### 目录结构说明

- **src/**: 源代码目录
  - **components/**: 可复用的UI组件
  - **pages/**: 页面级组件
  - **services/**: 业务逻辑服务
  - **store/**: 状态管理
  - **utils/**: 工具函数
  - **assets/**: 静态资源
  - **models/**: 3D模型文件
- **public/**: 静态资源目录
- **tests/**: 测试文件目录
- **配置文件**: 项目配置和依赖管理

## 🔗 组件关系图

```mermaid
graph TD
    A[main.jsx] --> B[App.jsx]
    
    B --> C[Header.jsx]
    B --> D[Sidebar.jsx]
    B --> E[Home.jsx]
    B --> F[DataInput.jsx]
    B --> G[Viewer3D.jsx]
    B --> H[History.jsx]
    
    D --> I[SettingsModal.jsx]
    G --> J[ControlPanel.jsx]
    
    %% Props传递
    B -.->|currentPage, onNavigate| C
    B -.->|isOpen, onToggle, currentPage, onNavigate| D
    B -.->|onNavigate| E
    B -.->|onNavigate| F
    B -.->|onNavigate| H
    
    D -.->|isOpen, settingType, onClose| I
    G -.->|playbackController, onScreenshot, onExport| J
    
    %% 数据流
    F -.->|setData| K[dataStore.js]
    G -.->|getData, subscribe| K
    H -.->|setData| K
    
    F -.->|parseCSV, parseManualInput| L[dataParser.js]
    
    G -.->|createPlaybackController| M[playback.js]
    J -.->|playback control| M
```

### 组件关系说明

**父子关系：**
- App.jsx是根组件，管理所有页面和全局状态
- Header和Sidebar是App的直接子组件
- 页面组件（Home、DataInput、Viewer3D、History）通过路由系统渲染
- SettingsModal是Sidebar的子组件
- ControlPanel是Viewer3D的子组件

**Props传递：**
- App向子组件传递导航函数和状态
- Sidebar向SettingsModal传递模态框控制props
- Viewer3D向ControlPanel传递播放控制器和回调函数

## 🔄 模块依赖图

```mermaid
graph LR
    A[dataParser.js] --> B[dataStore.js]
    B --> C[playback.js]
    C --> D[Viewer3D.jsx]
    
    E[DataInput.jsx] --> A
    E --> B
    
    F[History.jsx] --> B
    
    G[ControlPanel.jsx] --> C
    
    H[SettingsModal.jsx] --> I[localStorage]
    
    %% 服务层
    subgraph "Services Layer"
        A
    end
    
    %% 状态层
    subgraph "State Layer"
        B
        I
    end
    
    %% 工具层
    subgraph "Utils Layer"
        C
    end
    
    %% 组件层
    subgraph "Component Layer"
        D
        E
        F
        G
        H
    end
```

### 模块依赖说明

**数据流向：**
1. **dataParser.js**: 解析CSV文件和用户输入
2. **dataStore.js**: 存储和管理姿态数据，提供订阅机制
3. **playback.js**: 控制数据播放，管理动画状态
4. **localStorage**: 持久化用户设置

**调用关系：**
- DataInput组件调用dataParser解析数据，然后存储到dataStore
- Viewer3D组件从dataStore获取数据，使用playback控制播放
- ControlPanel组件操作playback控制器
- SettingsModal组件读写localStorage

## 📊 数据流图

```mermaid
sequenceDiagram
    participant U as 用户
    participant DI as DataInput
    participant DP as dataParser
    participant DS as dataStore
    participant V3D as Viewer3D
    participant PB as playback
    participant CP as ControlPanel
    
    %% 数据输入流程
    U->>DI: 上传CSV/手动输入
    DI->>DP: parseCSV/parseManualInput
    DP->>DI: 返回PosePoint[]
    DI->>DS: setData(poseData)
    DS->>DS: 验证数据格式
    DS->>DS: 触发订阅通知
    
    %% 3D展示流程
    DS->>V3D: 订阅通知(数据更新)
    V3D->>PB: createPlaybackController
    V3D->>PB: setData(poseData)
    
    %% 播放控制流程
    U->>CP: 点击播放按钮
    CP->>PB: play()
    PB->>V3D: onUpdate回调
    V3D->>V3D: updateSkeletonPose
    
    %% 设置管理流程
    U->>CP: 修改设置
    CP->>localStorage: 保存设置
    localStorage->>V3D: 设置更新事件
```

### 数据流程说明

**输入阶段：**
1. 用户在DataInput页面输入数据或上传CSV文件
2. dataParser解析数据为标准PosePoint格式
3. dataStore验证并存储数据，触发订阅通知

**展示阶段：**
4. Viewer3D接收数据更新通知
5. 创建playback控制器并设置数据
6. 初始化Three.js 3D场景

**交互阶段：**
7. 用户通过ControlPanel控制播放
8. playback控制器管理动画状态
9. 回调函数更新3D模型姿态

**设置阶段：**
10. 用户修改设置通过localStorage持久化
11. 设置更新触发相应的界面变化

## 🔌 API调用关系

```mermaid
graph TD
    subgraph "组件层"
        A[DataInput.jsx]
        B[Viewer3D.jsx]
        C[History.jsx]
        D[ControlPanel.jsx]
        E[SettingsModal.jsx]
        F[Sidebar.jsx]
    end

    subgraph "服务层"
        G[dataParser.js]
        H[dataStore.js]
        I[playback.js]
    end

    subgraph "存储层"
        J[localStorage]
    end

    %% dataParser API调用
    A -->|parseCSV()| G
    A -->|parseManualInput()| G
    A -->|convertAngleUnits()| G

    %% dataStore API调用
    A -->|setData()| H
    B -->|getData()| H
    B -->|subscribe()| H
    C -->|setData()| H

    %% playback API调用
    B -->|createPlaybackController()| I
    D -->|play(), pause(), reset()| I
    D -->|setSpeed(), seekTo()| I
    D -->|getStatus()| I

    %% localStorage API调用
    E -->|getItem(), setItem()| J
    F -->|getItem(), setItem()| J
    D -->|getItem(), setItem()| J
```

### API调用详情

#### dataParser.js API
- **parseCSV(fileContent)**: 解析CSV文件内容为PosePoint数组
- **parseManualInput(inputData)**: 解析用户手动输入数据
- **convertAngleUnits(posePoints, fromUnit, toUnit)**: 角度单位转换
- **degreesToRadians(degrees)**: 度转弧度
- **radiansToDegrees(radians)**: 弧度转度

#### dataStore.js API
- **setData(poseList)**: 存储姿态数据
- **getData()**: 获取姿态数据
- **getMetadata()**: 获取数据元信息
- **isDataLoaded()**: 检查数据加载状态
- **clearData()**: 清空数据
- **subscribe(callback)**: 订阅数据变更

#### playback.js API
- **createPlaybackController()**: 创建播放控制器实例
- **setData(poseData)**: 设置播放数据
- **play()**: 开始播放
- **pause()**: 暂停播放
- **reset()**: 重置播放
- **seekTo(index)**: 跳转到指定位置
- **setSpeed(speed)**: 设置播放速度
- **getStatus()**: 获取播放状态
- **on(event, callback)**: 设置事件回调

#### localStorage使用
- **设置存储**: angleUnit, lengthUnit, timeUnit, displaySettings, exportFormat
- **状态存储**: isRecording, recordStartTime
- **配置存储**: exportQuality, includeMetadata

## 📁 文件清单

### 🎯 核心应用文件
| 文件 | 作用 | 行数 | 依赖 |
|------|------|------|------|
| `src/main.jsx` | 应用入口，React根节点挂载 | ~15 | React, App.jsx |
| `src/App.jsx` | 主应用组件，路由和状态管理 | ~230 | 所有页面和基础组件 |
| `src/index.css` | 全局样式，Tailwind CSS配置 | ~50 | Tailwind CSS |

### 🧩 页面组件 (Pages)
| 文件 | 作用 | 行数 | 主要功能 |
|------|------|------|----------|
| `src/pages/Home.jsx` | 首页，系统介绍和导航 | ~280 | 功能展示、快速开始 |
| `src/pages/DataInput.jsx` | 数据输入页，文件上传和手动输入 | ~300 | CSV解析、数据验证 |
| `src/pages/Viewer3D.jsx` | 3D展示页，Three.js渲染 | ~420 | 3D场景、动画播放 |
| `src/pages/History.jsx` | 历史记录页，数据管理 | ~300 | 记录列表、数据回放 |

### 🔧 基础组件 (Components)
| 文件 | 作用 | 行数 | 主要功能 |
|------|------|------|----------|
| `src/components/Header.jsx` | 顶部导航栏 | ~80 | 页面导航、状态显示 |
| `src/components/Sidebar.jsx` | 侧边栏菜单 | ~320 | 设置菜单、工具功能 |
| `src/components/ControlPanel.jsx` | 播放控制面板 | ~280 | 播放控制、设置调整 |
| `src/components/SettingsModal.jsx` | 设置模态框 | ~300 | 单位设置、显示设置、导出设置 |

### 📊 服务层 (Services)
| 文件 | 作用 | 行数 | 主要功能 |
|------|------|------|----------|
| `src/services/dataParser.js` | 数据解析服务 | ~280 | CSV解析、数据验证、单位转换 |

### 🗄️ 状态管理 (Store)
| 文件 | 作用 | 行数 | 主要功能 |
|------|------|------|----------|
| `src/store/dataStore.js` | 全局数据存储 | ~150 | 数据管理、订阅机制、状态通知 |

### 🛠️ 工具模块 (Utils)
| 文件 | 作用 | 行数 | 主要功能 |
|------|------|------|----------|
| `src/utils/playback.js` | 播放控制器 | ~280 | 动画播放、速度控制、状态管理 |

### 📦 配置文件
| 文件 | 作用 | 主要配置 |
|------|------|----------|
| `package.json` | 项目配置和依赖 | React, Three.js, Tailwind CSS, Vite |
| `vite.config.js` | Vite构建配置 | 开发服务器、构建选项 |
| `tailwind.config.js` | Tailwind CSS配置 | 主题色彩、响应式断点 |
| `postcss.config.js` | PostCSS配置 | Tailwind和Autoprefixer |
| `index.html` | HTML入口文件 | 元数据、字体、加载动画 |

### 📋 文档和测试
| 目录/文件 | 作用 | 内容 |
|-----------|------|------|
| `README.md` | 项目说明文档 | 安装指南、使用说明、技术栈 |
| `ARCHITECTURE.md` | 架构文档 | 组件关系、数据流、API说明 |
| `tests/` | 测试文件目录 | 功能测试、调试工具、验证页面 |
| `sample-data.csv` | 示例数据文件 | 测试用的姿态数据 |

## 🎯 架构特点

### 优势
1. **模块化设计**: 清晰的分层架构，便于维护和扩展
2. **组件复用**: 基础组件可在多个页面中复用
3. **数据驱动**: 统一的数据流管理，状态可预测
4. **类型安全**: 严格的数据验证和错误处理
5. **性能优化**: 订阅机制减少不必要的重渲染

### 扩展性
- **新增页面**: 在pages目录添加新组件，在App.jsx中注册路由
- **新增功能**: 在services目录添加新的业务逻辑模块
- **新增组件**: 在components目录添加可复用组件
- **新增工具**: 在utils目录添加工具函数

### 维护性
- **单一职责**: 每个模块职责明确，便于定位问题
- **依赖清晰**: 模块间依赖关系明确，避免循环依赖
- **测试友好**: 模块化设计便于单元测试和集成测试

---

**文档版本**: 1.0.0
**更新时间**: 2024年当前时间
**维护者**: Attitude Tracker 3D开发团队
