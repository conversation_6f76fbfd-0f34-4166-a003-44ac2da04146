<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时预览功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .feature-list {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔴 3D视图实时更新功能测试</h1>
        
        <div class="status info">
            <strong>📋 功能概述</strong><br>
            测试当用户在数据输入页面输入姿态数据时，3D展示页面中的骨架模型能否实时反映姿态变化。
        </div>

        <h2>✨ 新增功能特性</h2>
        
        <div class="feature-list">
            <h3>🎯 核心功能</h3>
            <ul>
                <li>✅ <strong>实时预览开关</strong> - 数据输入页面新增实时预览复选框</li>
                <li>✅ <strong>即时姿态更新</strong> - 输入数据时3D模型立即更新</li>
                <li>✅ <strong>角度单位转换</strong> - 自动处理度/弧度转换</li>
                <li>✅ <strong>实时预览指示器</strong> - 3D页面显示预览状态</li>
                <li>✅ <strong>播放控制禁用</strong> - 预览模式下隐藏播放控制</li>
            </ul>
        </div>

        <h2>🧪 测试步骤</h2>

        <div class="test-section">
            <h3>步骤1: 启动应用并打开数据输入页面</h3>
            <div class="step">
                <strong>操作：</strong>
                <ol>
                    <li>确保开发服务器正在运行</li>
                    <li>打开应用: <a href="http://127.0.0.1:3001/" target="_blank" class="button">🚀 打开应用</a></li>
                    <li>点击导航栏中的"数据输入"</li>
                    <li>确保选择"手动输入"模式</li>
                </ol>
            </div>
            
            <div class="status success">
                <strong>预期结果：</strong> 看到手动输入表单，右上角有"⚪ 实时预览"复选框
            </div>
        </div>

        <div class="test-section">
            <h3>步骤2: 启用实时预览模式</h3>
            <div class="step">
                <strong>操作：</strong>
                <ol>
                    <li>勾选"实时预览"复选框</li>
                    <li>复选框应该变为"🔴 实时预览"</li>
                    <li>同时打开3D展示页面（新标签页）</li>
                    <li>在3D页面应该看到"🔴 实时预览模式"指示器</li>
                </ol>
            </div>
            
            <div class="status success">
                <strong>预期结果：</strong> 
                <ul>
                    <li>数据输入页面显示"🔴 实时预览"</li>
                    <li>3D页面左上角显示红色预览指示器</li>
                    <li>3D页面控制面板显示实时预览提示</li>
                    <li>播放控制按钮被隐藏</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>步骤3: 测试实时姿态更新</h3>
            <div class="step">
                <strong>测试数据组合：</strong>
                
                <div class="test-data">
                    <strong>测试1 - 基本旋转：</strong><br>
                    Pitch: 30°, Yaw: 45°, Roll: 15°<br>
                    X: 0, Y: 0, Z: 0
                </div>
                
                <div class="test-data">
                    <strong>测试2 - 位置移动：</strong><br>
                    Pitch: 0°, Yaw: 0°, Roll: 0°<br>
                    X: 2, Y: 1, Z: 1.5
                </div>
                
                <div class="test-data">
                    <strong>测试3 - 复合变换：</strong><br>
                    Pitch: -20°, Yaw: 90°, Roll: -10°<br>
                    X: -1, Y: 2, Z: 0.5
                </div>
                
                <div class="test-data">
                    <strong>测试4 - 极值测试：</strong><br>
                    Pitch: 90°, Yaw: 180°, Roll: -90°<br>
                    X: 3, Y: -2, Z: 2
                </div>
            </div>
            
            <div class="step">
                <strong>操作：</strong>
                <ol>
                    <li>在数据输入页面逐个输入上述测试数据</li>
                    <li>每次修改数值后立即观察3D页面</li>
                    <li>测试不同的角度单位（度/弧度）</li>
                    <li>验证位置和旋转变化是否正确</li>
                </ol>
            </div>
            
            <div class="status success">
                <strong>预期结果：</strong> 
                <ul>
                    <li>每次输入数值后，3D模型立即更新姿态</li>
                    <li>旋转角度变化正确反映在模型上</li>
                    <li>位置移动在3D空间中正确显示</li>
                    <li>角度单位切换时模型姿态相应调整</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>步骤4: 测试模式切换</h3>
            <div class="step">
                <strong>操作：</strong>
                <ol>
                    <li>在实时预览模式下输入一些数据</li>
                    <li>取消勾选"实时预览"复选框</li>
                    <li>点击"加载数据"按钮</li>
                    <li>观察页面跳转和播放控制恢复</li>
                </ol>
            </div>
            
            <div class="status success">
                <strong>预期结果：</strong> 
                <ul>
                    <li>取消实时预览后，3D页面指示器消失</li>
                    <li>点击加载数据后正常跳转到3D页面</li>
                    <li>播放控制面板正常显示</li>
                    <li>可以正常播放单帧动画</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>步骤5: 测试文件上传和示例数据</h3>
            <div class="step">
                <strong>操作：</strong>
                <ol>
                    <li>启用实时预览模式</li>
                    <li>切换到"文件上传"模式</li>
                    <li>上传CSV文件或生成示例数据</li>
                    <li>验证实时预览模式是否正确退出</li>
                </ol>
            </div>
            
            <div class="status success">
                <strong>预期结果：</strong> 
                <ul>
                    <li>上传文件或生成示例数据时自动退出实时预览</li>
                    <li>正常跳转到3D页面并开始播放动画</li>
                    <li>播放控制功能正常</li>
                </ul>
            </div>
        </div>

        <h2>🔧 技术验证点</h2>

        <div class="test-section">
            <h3>数据流验证</h3>
            <div class="step">
                <strong>验证要点：</strong>
                <ul>
                    <li>✅ <strong>数据存储</strong> - setRealTimePose()正确更新dataStore</li>
                    <li>✅ <strong>状态订阅</strong> - Viewer3D组件正确接收实时数据</li>
                    <li>✅ <strong>角度转换</strong> - 度转弧度计算准确</li>
                    <li>✅ <strong>模型更新</strong> - Three.js骨架姿态正确应用</li>
                    <li>✅ <strong>UI状态</strong> - 实时预览指示器正确显示</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>性能验证</h3>
            <div class="step">
                <strong>性能要求：</strong>
                <ul>
                    <li>🎯 <strong>响应时间</strong> - 输入到3D更新 < 100ms</li>
                    <li>🎯 <strong>渲染流畅</strong> - 连续输入时无卡顿</li>
                    <li>🎯 <strong>内存稳定</strong> - 长时间使用无内存泄漏</li>
                    <li>🎯 <strong>CPU占用</strong> - 实时更新时CPU使用合理</li>
                </ul>
            </div>
        </div>

        <h2>🐛 常见问题排查</h2>

        <div class="test-section">
            <h3>问题1: 实时预览不工作</h3>
            <div class="status error">
                <strong>可能原因：</strong>
                <ul>
                    <li>dataStore订阅机制失效</li>
                    <li>角度单位转换错误</li>
                    <li>3D场景未正确初始化</li>
                </ul>
            </div>
            
            <div class="step">
                <strong>排查步骤：</strong>
                <ol>
                    <li>打开浏览器开发者工具</li>
                    <li>检查Console是否有错误信息</li>
                    <li>验证setRealTimePose函数是否被调用</li>
                    <li>确认3D页面已正确加载</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>问题2: 模型姿态不正确</h3>
            <div class="step">
                <strong>检查要点：</strong>
                <ol>
                    <li>验证角度单位转换是否正确</li>
                    <li>检查Three.js旋转轴顺序</li>
                    <li>确认坐标系统一致性</li>
                    <li>测试已知角度值的预期结果</li>
                </ol>
            </div>
        </div>

        <h2>📊 测试报告</h2>

        <div class="test-section">
            <h3>测试结果记录</h3>
            <div class="step">
                <strong>请记录以下测试结果：</strong>
                <ul>
                    <li>□ 实时预览开关功能正常</li>
                    <li>□ 姿态数据实时更新正确</li>
                    <li>□ 角度单位转换准确</li>
                    <li>□ 3D模型响应及时</li>
                    <li>□ 预览指示器显示正确</li>
                    <li>□ 播放控制正确禁用/启用</li>
                    <li>□ 模式切换流畅</li>
                    <li>□ 性能表现良好</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="http://127.0.0.1:3001/" target="_blank" class="button" style="font-size: 18px; padding: 15px 30px;">
                🔍 开始实时预览测试
            </a>
        </div>
    </div>
</body>
</html>
