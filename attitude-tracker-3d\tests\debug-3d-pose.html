<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D姿态显示调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .debug-data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            border: 1px solid #dee2e6;
            font-size: 14px;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 3D姿态显示问题调试</h1>
        
        <div class="status error">
            <strong>🐛 问题描述</strong><br>
            设置角度后点击加载数据，3D显示并没有出现相应的姿态和位置变化
        </div>

        <h2>🎯 问题诊断步骤</h2>

        <div class="test-section">
            <h3>步骤1: 验证数据输入和转换</h3>
            <div class="step">
                <strong>测试数据：</strong>
                <div class="debug-data">
                    输入数据: pitch=30°, yaw=45°, roll=15°, x=1, y=2, z=0.5<br>
                    预期转换: pitch=0.524rad, yaw=0.785rad, roll=0.262rad
                </div>
                
                <strong>检查步骤：</strong>
                <ol>
                    <li>打开应用数据输入页面</li>
                    <li>输入上述测试数据</li>
                    <li>打开浏览器开发者工具 (F12)</li>
                    <li>点击"加载数据"按钮</li>
                    <li>在Console中查看是否有"更新骨架姿态"的日志</li>
                </ol>
                
                <div class="status info">
                    <strong>预期日志输出：</strong><br>
                    更新骨架姿态: {pitch: 0.524, yaw: 0.785, roll: 0.262, x: 1, y: 2, z: 0.5}
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>步骤2: 检查3D场景初始化</h3>
            <div class="step">
                <strong>验证要点：</strong>
                <ol>
                    <li>3D页面是否正常加载</li>
                    <li>是否看到蓝色的骨架模型</li>
                    <li>是否看到网格和坐标轴</li>
                    <li>控制面板是否显示</li>
                </ol>
                
                <div class="status warning">
                    <strong>如果3D场景没有显示：</strong><br>
                    检查浏览器是否支持WebGL，或者有JavaScript错误
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>步骤3: 测试已知角度值</h3>
            <div class="step">
                <strong>标准测试用例：</strong>
                
                <div class="two-column">
                    <div>
                        <h4>测试1: 纯旋转</h4>
                        <div class="debug-data">
                            Pitch: 90° (向前倾)<br>
                            Yaw: 0°<br>
                            Roll: 0°<br>
                            Position: (0, 0, 0)
                        </div>
                        <strong>预期结果：</strong> 模型向前倾斜90度
                    </div>
                    
                    <div>
                        <h4>测试2: 纯位移</h4>
                        <div class="debug-data">
                            Pitch: 0°<br>
                            Yaw: 0°<br>
                            Roll: 0°<br>
                            Position: (2, 1, 1)
                        </div>
                        <strong>预期结果：</strong> 模型移动到新位置
                    </div>
                </div>
                
                <div class="two-column">
                    <div>
                        <h4>测试3: Yaw旋转</h4>
                        <div class="debug-data">
                            Pitch: 0°<br>
                            Yaw: 90° (左转)<br>
                            Roll: 0°<br>
                            Position: (0, 0, 0)
                        </div>
                        <strong>预期结果：</strong> 模型左转90度
                    </div>
                    
                    <div>
                        <h4>测试4: Roll旋转</h4>
                        <div class="debug-data">
                            Pitch: 0°<br>
                            Yaw: 0°<br>
                            Roll: 45° (侧倾)<br>
                            Position: (0, 0, 0)
                        </div>
                        <strong>预期结果：</strong> 模型侧倾45度
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>步骤4: 检查数据流</h3>
            <div class="step">
                <strong>数据流验证：</strong>
                <ol>
                    <li><strong>输入 →</strong> parseManualInput() 解析数据</li>
                    <li><strong>转换 →</strong> degreesToRadians() 角度转换</li>
                    <li><strong>存储 →</strong> setData() 保存到dataStore</li>
                    <li><strong>订阅 →</strong> Viewer3D接收数据变化</li>
                    <li><strong>播放 →</strong> playbackController设置数据</li>
                    <li><strong>更新 →</strong> updateSkeletonPose() 更新模型</li>
                    <li><strong>渲染 →</strong> Three.js渲染新姿态</li>
                </ol>
                
                <div class="status info">
                    <strong>调试技巧：</strong><br>
                    在每个步骤添加console.log()来跟踪数据流
                </div>
            </div>
        </div>

        <h2>🔧 常见问题和解决方案</h2>

        <div class="test-section">
            <h3>问题1: 角度转换错误</h3>
            <div class="step">
                <strong>症状：</strong> 模型旋转角度不正确<br>
                <strong>原因：</strong> 度转弧度计算错误或重复转换<br>
                <strong>解决：</strong> 确保只在必要时进行一次角度转换
                
                <div class="debug-data">
                    正确转换: degrees * (Math.PI / 180)<br>
                    30° = 30 * (3.14159 / 180) = 0.524 rad
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>问题2: 欧拉角顺序错误</h3>
            <div class="step">
                <strong>症状：</strong> 旋转方向不符合预期<br>
                <strong>原因：</strong> Three.js欧拉角顺序与预期不符<br>
                <strong>解决：</strong> 验证XYZ顺序是否正确
                
                <div class="debug-data">
                    Three.js默认: rotation.set(x, y, z) = (pitch, yaw, roll)<br>
                    可能需要调整为: rotation.set(pitch, yaw, roll)
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>问题3: 初始姿态未显示</h3>
            <div class="step">
                <strong>症状：</strong> 加载数据后模型保持初始状态<br>
                <strong>原因：</strong> 未立即显示第一帧<br>
                <strong>解决：</strong> 在setData后立即调用updateSkeletonPose
                
                <div class="debug-data">
                    playbackController.setData(poseData);<br>
                    if (poseData.length > 0) {<br>
                    &nbsp;&nbsp;updateSkeletonPose(poseData[0]);<br>
                    }
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>问题4: 渲染未更新</h3>
            <div class="step">
                <strong>症状：</strong> 数据更新但视觉无变化<br>
                <strong>原因：</strong> 渲染循环问题<br>
                <strong>解决：</strong> 确保有持续的渲染循环或手动触发渲染
                
                <div class="debug-data">
                    // 手动触发渲染<br>
                    renderer.render(scene, camera);
                </div>
            </div>
        </div>

        <h2>🧪 快速测试工具</h2>

        <div class="test-section">
            <h3>角度转换验证器</h3>
            <div class="step">
                <strong>在浏览器Console中运行：</strong>
                <div class="debug-data">
                    // 测试角度转换<br>
                    function testAngleConversion(degrees) {<br>
                    &nbsp;&nbsp;const radians = degrees * (Math.PI / 180);<br>
                    &nbsp;&nbsp;console.log(`${degrees}° = ${radians.toFixed(3)} rad`);<br>
                    &nbsp;&nbsp;return radians;<br>
                    }<br><br>
                    
                    testAngleConversion(30);  // 应该输出 0.524<br>
                    testAngleConversion(45);  // 应该输出 0.785<br>
                    testAngleConversion(90);  // 应该输出 1.571
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Three.js状态检查器</h3>
            <div class="step">
                <strong>在3D页面Console中运行：</strong>
                <div class="debug-data">
                    // 检查骨架状态<br>
                    function checkSkeletonState() {<br>
                    &nbsp;&nbsp;const skeleton = window.skeletonRef?.current;<br>
                    &nbsp;&nbsp;if (skeleton) {<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;console.log('Position:', skeleton.position);<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;console.log('Rotation:', skeleton.rotation);<br>
                    &nbsp;&nbsp;} else {<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;console.log('Skeleton not found');<br>
                    &nbsp;&nbsp;}<br>
                    }<br><br>
                    
                    checkSkeletonState();
                </div>
            </div>
        </div>

        <h2>📋 测试清单</h2>

        <div class="test-section">
            <div class="step">
                <strong>完成以下检查项：</strong>
                <ul>
                    <li>□ 数据输入页面正常显示</li>
                    <li>□ 角度转换计算正确</li>
                    <li>□ Console显示"更新骨架姿态"日志</li>
                    <li>□ 3D场景正常初始化</li>
                    <li>□ 骨架模型可见</li>
                    <li>□ 测试数据产生预期的视觉变化</li>
                    <li>□ 位置移动正确显示</li>
                    <li>□ 旋转角度正确应用</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="http://127.0.0.1:3002/" target="_blank" class="button" style="font-size: 18px; padding: 15px 30px;">
                🔍 开始调试测试
            </a>
        </div>
    </div>
</body>
</html>
