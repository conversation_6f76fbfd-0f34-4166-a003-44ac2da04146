# Attitude Tracker 3D - 测试指南

## 🚀 快速启动

1. **启动开发服务器**
   ```bash
   cd attitude-tracker-3d
   npm run dev
   ```

2. **打开浏览器**
   访问: http://localhost:3000

## 🧪 功能测试清单

### ✅ 1. 首页测试
- [ ] 页面正常加载，显示系统标题和介绍
- [ ] 功能卡片显示正确（数据输入、3D可视化、历史记录）
- [ ] 快速开始按钮可点击
- [ ] 导航菜单正常工作
- [ ] 响应式设计在不同屏幕尺寸下正常

### ✅ 2. 数据输入页面测试

#### 手动输入测试
- [ ] 切换到"手动输入"模式
- [ ] 输入姿态数据（pitch: 10, yaw: 20, roll: 5, x: 1, y: 2, z: 3）
- [ ] 角度单位切换（度/弧度）正常
- [ ] 点击"加载数据"按钮
- [ ] 显示成功消息并自动跳转到3D展示页

#### 文件上传测试
- [ ] 切换到"文件上传"模式
- [ ] 上传提供的 `sample-data.csv` 文件
- [ ] 文件解析成功，显示数据点数量
- [ ] 自动跳转到3D展示页面

#### 示例数据测试
- [ ] 点击"生成示例数据"按钮
- [ ] 生成100个示例数据点
- [ ] 自动跳转到3D展示页面

### ✅ 3. 3D展示页面测试

#### 场景渲染测试
- [ ] 3D场景正常加载
- [ ] 骨架模型正确显示（蓝色主体、黄色头部、绿色手臂、红色腿部）
- [ ] 网格和坐标轴显示
- [ ] 光照效果正常

#### 交互控制测试
- [ ] 鼠标拖拽旋转视角
- [ ] 鼠标滚轮缩放
- [ ] 控制面板显示在右侧

#### 播放控制测试
- [ ] 播放按钮启动动画
- [ ] 暂停按钮停止动画
- [ ] 重置按钮回到初始状态
- [ ] 进度条显示当前播放进度
- [ ] 速度调节（0.25x - 2x）正常工作

#### 设置功能测试
- [ ] 角度单位切换
- [ ] 显示设置选项（轨迹、相机跟随等）
- [ ] 截图功能（下载PNG文件）
- [ ] 导出功能（下载JSON文件）

### ✅ 4. 历史记录页面测试
- [ ] 显示模拟历史记录列表
- [ ] 搜索功能正常
- [ ] 排序功能（按日期、名称、大小）
- [ ] 记录卡片显示缩略图和信息
- [ ] 点击"加载"按钮跳转到3D展示
- [ ] 点击"详情"按钮显示详细信息
- [ ] 导出和删除功能正常

### ✅ 5. 导航和路由测试
- [ ] 顶部导航栏正常工作
- [ ] 侧边栏（在支持的页面）正常显示
- [ ] 页面间切换流畅
- [ ] 键盘快捷键（Ctrl+1-4）正常工作
- [ ] 移动端侧边栏切换正常

## 🔧 性能测试

### 数据处理性能
- [ ] 上传大型CSV文件（1000+数据点）
- [ ] 播放长时间动画序列
- [ ] 快速切换播放速度
- [ ] 频繁跳转播放位置

### 3D渲染性能
- [ ] 长时间播放动画无卡顿
- [ ] 频繁视角变换流畅
- [ ] 多次截图操作正常
- [ ] 浏览器标签页切换后恢复正常

## 🐛 错误处理测试

### 数据输入错误
- [ ] 上传无效CSV文件
- [ ] 输入非数字值
- [ ] 上传空文件
- [ ] 网络中断时的处理

### 3D渲染错误
- [ ] 无数据时的提示
- [ ] WebGL不支持时的降级
- [ ] 内存不足时的处理

## 📱 兼容性测试

### 浏览器兼容性
- [ ] Chrome (最新版本)
- [ ] Firefox (最新版本)
- [ ] Safari (最新版本)
- [ ] Edge (最新版本)

### 设备兼容性
- [ ] 桌面端（1920x1080及以上）
- [ ] 平板端（768px-1024px）
- [ ] 移动端（320px-768px）

## 📊 测试数据

### 示例CSV数据
项目包含 `sample-data.csv` 文件，包含21个数据点的姿态轨迹。

### 手动测试数据
```
pitch: 15°, yaw: 30°, roll: -10°
x: 1.5m, y: 2.0m, z: 0.5m
```

## 🎯 预期结果

### 成功标准
- ✅ 所有核心功能正常工作
- ✅ 无JavaScript错误
- ✅ 3D渲染流畅（>30fps）
- ✅ 数据处理准确
- ✅ 用户界面响应迅速

### 性能指标
- 页面加载时间 < 3秒
- 3D场景初始化 < 2秒
- CSV文件解析 < 1秒（1000行以内）
- 动画播放帧率 > 30fps

## 🔍 调试信息

### 开发者工具
- 打开浏览器开发者工具查看控制台
- 检查网络请求状态
- 监控内存使用情况

### 日志输出
应用会在控制台输出关键操作的日志信息，包括：
- 数据加载状态
- 3D场景初始化
- 播放控制操作
- 错误信息

## 📞 问题反馈

如果发现任何问题，请记录：
1. 操作步骤
2. 预期结果
3. 实际结果
4. 浏览器和版本
5. 控制台错误信息
