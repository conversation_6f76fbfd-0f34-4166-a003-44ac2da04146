/**
 * 控制面板组件
 * 用途：提供播放控制、设置调整等交互功能
 */

import React, { useState, useEffect } from 'react';

const ControlPanel = ({
  playbackController,
  onScreenshot,
  onExport,
  isRealTimeMode = false,
  className = ''
}) => {
  const [playbackStatus, setPlaybackStatus] = useState({
    isPlaying: false,
    currentIndex: 0,
    totalFrames: 0,
    progress: 0,
    playbackSpeed: 1.0
  });

  const [settings, setSettings] = useState({
    angleUnit: 'degrees', // 'degrees' | 'radians'
    autoLoop: false,
    showTrajectory: true,
    cameraFollow: false
  });

  // 监听播放状态变化
  useEffect(() => {
    if (!playbackController) return;

    const updateStatus = () => {
      setPlaybackStatus(playbackController.getStatus());
    };

    // 设置播放控制器回调
    playbackController.on('onUpdate', updateStatus);
    playbackController.on('onStart', updateStatus);
    playbackController.on('onPause', updateStatus);
    playbackController.on('onComplete', updateStatus);

    return () => {
      // 清理回调
      playbackController.on('onUpdate', null);
      playbackController.on('onStart', null);
      playbackController.on('onPause', null);
      playbackController.on('onComplete', null);
    };
  }, [playbackController]);

  // 播放控制函数
  const handlePlay = () => {
    if (!playbackController) return;
    playbackController.play();
  };

  const handlePause = () => {
    if (!playbackController) return;
    playbackController.pause();
  };

  const handleReset = () => {
    if (!playbackController) return;
    playbackController.reset();
  };

  const handleSpeedChange = (speed) => {
    if (!playbackController) return;
    playbackController.setSpeed(speed);
    setPlaybackStatus(prev => ({ ...prev, playbackSpeed: speed }));
  };

  const handleSeek = (progress) => {
    if (!playbackController) return;
    const targetIndex = Math.floor(progress * playbackStatus.totalFrames);
    playbackController.seekTo(targetIndex);
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className={`bg-gray-800 rounded-lg p-4 space-y-4 ${className}`}>
      {/* 实时预览模式提示 */}
      {isRealTimeMode && (
        <div className="bg-red-900 border border-red-700 rounded-lg p-3">
          <h3 className="text-lg font-semibold text-white flex items-center">
            <span className="mr-2">🔴</span>
            实时预览模式
          </h3>
          <p className="text-sm text-red-300 mt-1">
            当前正在实时预览姿态数据，播放控制已禁用
          </p>
        </div>
      )}

      {/* 播放控制区域 */}
      {!isRealTimeMode && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-white flex items-center">
            <span className="mr-2">🎮</span>
            播放控制
          </h3>

        {/* 播放按钮组 */}
        <div className="flex items-center space-x-2">
          <button
            onClick={playbackStatus.isPlaying ? handlePause : handlePlay}
            className="btn-primary flex items-center space-x-2"
            disabled={playbackStatus.totalFrames === 0}
          >
            <span>{playbackStatus.isPlaying ? '⏸️' : '▶️'}</span>
            <span>{playbackStatus.isPlaying ? '暂停' : '播放'}</span>
          </button>

          <button
            onClick={handleReset}
            className="btn-secondary flex items-center space-x-2"
          >
            <span>🔄</span>
            <span>重置</span>
          </button>

          <div className="flex items-center space-x-2 ml-4">
            <span className="text-sm text-gray-400">速度:</span>
            <select
              value={playbackStatus.playbackSpeed}
              onChange={(e) => handleSpeedChange(parseFloat(e.target.value))}
              className="input-field text-sm py-1"
            >
              <option value={0.25}>0.25x</option>
              <option value={0.5}>0.5x</option>
              <option value={1}>1x</option>
              <option value={1.5}>1.5x</option>
              <option value={2}>2x</option>
            </select>
          </div>
        </div>

        {/* 进度条 */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-400">
            <span>进度: {playbackStatus.currentIndex} / {playbackStatus.totalFrames}</span>
            <span>{Math.round(playbackStatus.progress * 100)}%</span>
          </div>
          <input
            type="range"
            min="0"
            max="1"
            step="0.001"
            value={playbackStatus.progress}
            onChange={(e) => handleSeek(parseFloat(e.target.value))}
            className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
          />
        </div>
        </div>
      )}

      {/* 设置区域 */}
      <div className="space-y-3 border-t border-gray-700 pt-4">
        <h3 className="text-lg font-semibold text-white flex items-center">
          <span className="mr-2">⚙️</span>
          显示设置
        </h3>

        <div className="grid grid-cols-2 gap-3">
          {/* 角度单位设置 */}
          <div className="space-y-1">
            <label className="text-sm text-gray-400">角度单位</label>
            <select
              value={settings.angleUnit}
              onChange={(e) => handleSettingChange('angleUnit', e.target.value)}
              className="input-field text-sm w-full"
            >
              <option value="degrees">度 (°)</option>
              <option value="radians">弧度 (rad)</option>
            </select>
          </div>

          {/* 自动循环 */}
          <div className="space-y-1">
            <label className="text-sm text-gray-400">自动循环</label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.autoLoop}
                onChange={(e) => handleSettingChange('autoLoop', e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-white">启用</span>
            </label>
          </div>

          {/* 显示轨迹 */}
          <div className="space-y-1">
            <label className="text-sm text-gray-400">显示轨迹</label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.showTrajectory}
                onChange={(e) => handleSettingChange('showTrajectory', e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-white">启用</span>
            </label>
          </div>

          {/* 相机跟随 */}
          <div className="space-y-1">
            <label className="text-sm text-gray-400">相机跟随</label>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.cameraFollow}
                onChange={(e) => handleSettingChange('cameraFollow', e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-white">启用</span>
            </label>
          </div>
        </div>
      </div>

      {/* 操作按钮区域 */}
      <div className="space-y-3 border-t border-gray-700 pt-4">
        <h3 className="text-lg font-semibold text-white flex items-center">
          <span className="mr-2">🔧</span>
          操作工具
        </h3>

        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={onScreenshot}
            className="btn-secondary flex items-center justify-center space-x-2"
          >
            <span>📸</span>
            <span>截图</span>
          </button>

          <button
            onClick={onExport}
            className="btn-secondary flex items-center justify-center space-x-2"
          >
            <span>💾</span>
            <span>导出</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ControlPanel;
