/**
 * 应用主框架组件
 * 用途：提供路由与页面切换结构，管理全局状态
 */

import React, { useState, useEffect } from 'react';
import Header from './components/Header.jsx';
import Sidebar from './components/Sidebar.jsx';
import Home from './pages/Home.jsx';
import DataInput from './pages/DataInput.jsx';
import Viewer3D from './pages/Viewer3D.jsx';
import History from './pages/History.jsx';

const App = () => {
  const [currentPage, setCurrentPage] = useState('home');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 页面配置
  const pages = {
    home: { component: Home, title: '首页', showSidebar: true },
    input: { component: DataInput, title: '数据输入', showSidebar: true },
    viewer: { component: Viewer3D, title: '3D展示', showSidebar: false },
    history: { component: History, title: '历史记录', showSidebar: true }
  };

  // 应用初始化
  useEffect(() => {
    // 模拟应用加载过程
    const initializeApp = async () => {
      try {
        // 这里可以添加应用初始化逻辑
        // 例如：检查浏览器兼容性、加载配置等
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setIsLoading(false);
      } catch (error) {
        console.error('应用初始化失败:', error);
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  // 页面导航处理
  const handleNavigate = (pageId) => {
    if (pages[pageId]) {
      setCurrentPage(pageId);
      setIsSidebarOpen(false); // 导航后关闭侧边栏
      
      // 更新页面标题
      document.title = `${pages[pageId].title} - Attitude Tracker 3D`;
    }
  };

  // 侧边栏切换
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // 获取当前页面组件
  const getCurrentPageComponent = () => {
    const pageConfig = pages[currentPage];
    if (!pageConfig) {
      return <ErrorPage onNavigate={handleNavigate} />;
    }

    const PageComponent = pageConfig.component;
    return <PageComponent onNavigate={handleNavigate} />;
  };

  // 检查是否显示侧边栏
  const shouldShowSidebar = () => {
    const pageConfig = pages[currentPage];
    return pageConfig && pageConfig.showSidebar;
  };

  // 应用加载中
  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col">
      {/* 顶部导航栏 */}
      <Header 
        currentPage={currentPage}
        onNavigate={handleNavigate}
      />

      {/* 主要内容区域 */}
      <div className="flex-1 flex">
        {/* 侧边栏 */}
        {shouldShowSidebar() && (
          <Sidebar
            isOpen={isSidebarOpen}
            onToggle={toggleSidebar}
            currentPage={currentPage}
            onNavigate={handleNavigate}
          />
        )}

        {/* 页面内容 */}
        <main className={`flex-1 ${shouldShowSidebar() ? 'lg:ml-64' : ''}`}>
          {getCurrentPageComponent()}
        </main>

        {/* 侧边栏切换按钮（移动端） */}
        {shouldShowSidebar() && (
          <button
            onClick={toggleSidebar}
            className="lg:hidden fixed bottom-6 right-6 z-50 w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg flex items-center justify-center transition-colors duration-200"
          >
            <span className="text-xl">☰</span>
          </button>
        )}
      </div>

      {/* 全局快捷键提示 */}
      <KeyboardShortcuts currentPage={currentPage} onNavigate={handleNavigate} />
    </div>
  );
};

/**
 * 加载屏幕组件
 */
const LoadingScreen = () => {
  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        {/* Logo动画 */}
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-primary-600 rounded-full mb-4 animate-pulse">
            <span className="text-3xl font-bold text-white">3D</span>
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">
            Attitude Tracker 3D
          </h1>
          <p className="text-gray-400">
            正在初始化应用...
          </p>
        </div>

        {/* 加载动画 */}
        <div className="flex justify-center space-x-2">
          <div className="w-3 h-3 bg-primary-600 rounded-full animate-bounce"></div>
          <div className="w-3 h-3 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-3 h-3 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </div>
  );
};

/**
 * 错误页面组件
 */
const ErrorPage = ({ onNavigate }) => {
  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="text-6xl mb-4">❌</div>
        <h1 className="text-3xl font-bold text-white mb-4">
          页面未找到
        </h1>
        <p className="text-gray-400 mb-8">
          抱歉，您访问的页面不存在
        </p>
        <button
          onClick={() => onNavigate('home')}
          className="btn-primary"
        >
          返回首页
        </button>
      </div>
    </div>
  );
};

/**
 * 键盘快捷键处理组件
 */
const KeyboardShortcuts = ({ currentPage, onNavigate }) => {
  useEffect(() => {
    const handleKeyDown = (event) => {
      // 检查是否按下了Ctrl/Cmd键
      const isCtrlOrCmd = event.ctrlKey || event.metaKey;
      
      if (isCtrlOrCmd) {
        switch (event.key) {
          case '1':
            event.preventDefault();
            onNavigate('home');
            break;
          case '2':
            event.preventDefault();
            onNavigate('input');
            break;
          case '3':
            event.preventDefault();
            onNavigate('viewer');
            break;
          case '4':
            event.preventDefault();
            onNavigate('history');
            break;
          default:
            break;
        }
      }

      // ESC键处理
      if (event.key === 'Escape') {
        // 可以添加ESC键的处理逻辑，比如关闭模态框等
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onNavigate]);

  return null; // 这个组件不渲染任何内容
};

export default App;
