# Attitude Tracker 3D

基于Web的三维姿态追踪系统，使用 React + Three.js 实现实时数据可视化、动画播放和轨迹分析。

## 功能特性

- 📊 **数据输入**: 支持手动输入或CSV文件上传姿态数据
- 🎯 **3D可视化**: 实时三维姿态展示和动画播放
- 📋 **历史记录**: 查看和管理历史姿态数据记录
- 🎮 **播放控制**: 播放、暂停、重置、速度调节
- 📸 **截图导出**: 支持截图和数据导出功能
- 📐 **单位转换**: 支持角度制和弧度制切换

## 技术栈

- **前端框架**: React 18
- **3D引擎**: Three.js
- **样式框架**: Tailwind CSS
- **构建工具**: Vite
- **开发语言**: JavaScript (ES6+)

## 项目结构

```
attitude-tracker-3d/
├── public/
│   └── index.html              # HTML入口文件
├── src/
│   ├── App.jsx                 # 应用主框架
│   ├── main.jsx                # 应用入口
│   ├── index.css               # 全局样式
│   ├── components/             # 基础组件
│   │   ├── Header.jsx          # 顶部导航栏
│   │   ├── Sidebar.jsx         # 侧边栏
│   │   └── ControlPanel.jsx    # 控制面板
│   ├── pages/                  # 页面组件
│   │   ├── Home.jsx            # 首页
│   │   ├── DataInput.jsx       # 数据输入页
│   │   ├── Viewer3D.jsx        # 3D展示页
│   │   └── History.jsx         # 历史记录页
│   ├── services/               # 服务模块
│   │   └── dataParser.js       # 数据解析服务
│   ├── store/                  # 状态管理
│   │   └── dataStore.js        # 数据存储
│   ├── utils/                  # 工具模块
│   │   └── playback.js         # 播放控制器
│   ├── assets/                 # 静态资源
│   └── models/                 # 3D模型文件
├── package.json                # 项目配置
├── vite.config.js              # Vite配置
├── tailwind.config.js          # Tailwind配置
└── postcss.config.js           # PostCSS配置
```

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0

### 安装依赖

```bash
cd attitude-tracker-3d
npm install
```

### 启动开发服务器

```bash
npm run dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 数据格式

### CSV文件格式

CSV文件应包含以下列（顺序必须一致）：

```csv
timestamp,pitch,yaw,roll,x,y,z
1640995200000,10.5,-5.2,2.1,0.5,1.2,0.8
1640995200100,11.2,-4.8,2.3,0.6,1.3,0.9
```

- **timestamp**: 时间戳（毫秒）
- **pitch**: 俯仰角（度或弧度）
- **yaw**: 偏航角（度或弧度）
- **roll**: 翻滚角（度或弧度）
- **x, y, z**: 三维坐标（米）

### 手动输入

支持单个姿态点的手动输入，包括：
- 三个旋转角度（pitch, yaw, roll）
- 三个位置坐标（x, y, z）
- 角度单位可选择度或弧度

## 使用说明

1. **数据输入**: 在数据输入页面手动输入数据或上传CSV文件
2. **3D展示**: 查看三维姿态可视化和动画播放
3. **播放控制**: 使用控制面板调节播放速度、截图等
4. **历史记录**: 管理和回放历史数据记录

## 键盘快捷键

- `Ctrl/Cmd + 1`: 跳转到首页
- `Ctrl/Cmd + 2`: 跳转到数据输入页
- `Ctrl/Cmd + 3`: 跳转到3D展示页
- `Ctrl/Cmd + 4`: 跳转到历史记录页

## 浏览器兼容性

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 开发规范

- 遵循 fail-fast 原则，避免使用 try-catch 掩盖错误
- 每个文件不超过 500 行代码
- 低层模块不调用高层模块
- 优先复用已有代码

## 许可证

MIT License

## 版本信息

当前版本: 1.0.0
