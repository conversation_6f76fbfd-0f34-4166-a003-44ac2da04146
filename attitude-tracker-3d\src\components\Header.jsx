/**
 * 顶部导航栏组件
 * 用途：显示系统标题、导航链接和状态信息
 */

import React from 'react';

const Header = ({ currentPage, onNavigate }) => {
  const navigationItems = [
    { id: 'home', label: '首页', icon: '🏠' },
    { id: 'input', label: '数据输入', icon: '📊' },
    { id: 'viewer', label: '3D展示', icon: '🎯' },
    { id: 'history', label: '历史记录', icon: '📋' }
  ];

  return (
    <header className="bg-gray-900 border-b border-gray-700 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* 系统标题 */}
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">3D</span>
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">
              Attitude Tracker 3D
            </h1>
            <p className="text-sm text-gray-400">
              三维姿态追踪系统
            </p>
          </div>
        </div>

        {/* 导航菜单 */}
        <nav className="flex items-center space-x-1">
          {navigationItems.map((item) => (
            <button
              key={item.id}
              onClick={() => onNavigate && onNavigate(item.id)}
              className={`
                flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors duration-200
                ${currentPage === item.id
                  ? 'bg-primary-600 text-white'
                  : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                }
              `}
            >
              <span className="text-lg">{item.icon}</span>
              <span className="font-medium">{item.label}</span>
            </button>
          ))}
        </nav>

        {/* 状态指示器 */}
        <div className="flex items-center space-x-4">
          <StatusIndicator />
          <div className="text-sm text-gray-400">
            {new Date().toLocaleTimeString()}
          </div>
        </div>
      </div>
    </header>
  );
};

/**
 * 状态指示器组件
 */
const StatusIndicator = () => {
  return (
    <div className="flex items-center space-x-2">
      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
      <span className="text-sm text-gray-400">系统正常</span>
    </div>
  );
};

export default Header;
