/**
 * 3D可视化页面组件
 * 用途：Three.js三维姿态展示和动画播放
 */

import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { getData, subscribe } from '../store/dataStore.js';
import { createPlaybackController } from '../utils/playback.js';
import ControlPanel from '../components/ControlPanel.jsx';

const Viewer3D = () => {
  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const cameraRef = useRef(null);
  const skeletonRef = useRef(null);
  const playbackControllerRef = useRef(null);
  
  const [isSceneReady, setIsSceneReady] = useState(false);
  const [poseData, setPoseData] = useState([]);
  const [error, setError] = useState('');

  // 初始化Three.js场景
  useEffect(() => {
    if (!mountRef.current) return;

    try {
      initScene();
      setIsSceneReady(true);
    } catch (err) {
      setError('3D场景初始化失败: ' + err.message);
    }

    return () => {
      cleanup();
    };
  }, []);

  // 监听数据变化
  useEffect(() => {
    const unsubscribe = subscribe((state) => {
      setPoseData(state.currentData);
      if (playbackControllerRef.current && state.currentData.length > 0) {
        playbackControllerRef.current.setData(state.currentData);
      }
    });

    // 初始加载数据
    const initialData = getData();
    if (initialData.length > 0) {
      setPoseData(initialData);
    }

    return unsubscribe;
  }, []);

  // 初始化播放控制器
  useEffect(() => {
    if (isSceneReady && poseData.length > 0) {
      if (!playbackControllerRef.current) {
        playbackControllerRef.current = createPlaybackController();
        
        // 设置播放回调
        playbackControllerRef.current.on('onUpdate', (posePoint) => {
          updateSkeletonPose(posePoint);
        });
      }
      
      playbackControllerRef.current.setData(poseData);
    }
  }, [isSceneReady, poseData]);

  /**
   * 初始化Three.js场景
   */
  const initScene = () => {
    const container = mountRef.current;
    const width = container.clientWidth;
    const height = container.clientHeight;

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x0f172a);
    sceneRef.current = scene;

    // 创建相机
    const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    camera.position.set(5, 5, 5);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(width, height);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    rendererRef.current = renderer;

    container.appendChild(renderer.domElement);

    // 添加光源
    addLights(scene);

    // 创建骨架模型
    createSkeleton(scene);

    // 添加网格和坐标轴
    addGridAndAxes(scene);

    // 添加控制器
    addControls(camera, renderer);

    // 开始渲染循环
    animate();
  };

  /**
   * 添加光源
   */
  const addLights = (scene) => {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // 点光源
    const pointLight = new THREE.PointLight(0x3b82f6, 0.5, 100);
    pointLight.position.set(0, 5, 0);
    scene.add(pointLight);
  };

  /**
   * 创建骨架模型
   */
  const createSkeleton = (scene) => {
    const skeletonGroup = new THREE.Group();
    
    // 主体（立方体）
    const bodyGeometry = new THREE.BoxGeometry(1, 2, 0.5);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x3b82f6 });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.castShadow = true;
    skeletonGroup.add(body);

    // 头部（球体）
    const headGeometry = new THREE.SphereGeometry(0.3, 16, 16);
    const headMaterial = new THREE.MeshLambertMaterial({ color: 0xfbbf24 });
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.set(0, 1.3, 0);
    head.castShadow = true;
    skeletonGroup.add(head);

    // 手臂
    const armGeometry = new THREE.CylinderGeometry(0.1, 0.1, 1.5);
    const armMaterial = new THREE.MeshLambertMaterial({ color: 0x10b981 });
    
    const leftArm = new THREE.Mesh(armGeometry, armMaterial);
    leftArm.position.set(-0.8, 0.5, 0);
    leftArm.rotation.z = Math.PI / 4;
    leftArm.castShadow = true;
    skeletonGroup.add(leftArm);

    const rightArm = new THREE.Mesh(armGeometry, armMaterial);
    rightArm.position.set(0.8, 0.5, 0);
    rightArm.rotation.z = -Math.PI / 4;
    rightArm.castShadow = true;
    skeletonGroup.add(rightArm);

    // 腿部
    const legGeometry = new THREE.CylinderGeometry(0.15, 0.15, 1.8);
    const legMaterial = new THREE.MeshLambertMaterial({ color: 0xef4444 });
    
    const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
    leftLeg.position.set(-0.3, -1.9, 0);
    leftLeg.castShadow = true;
    skeletonGroup.add(leftLeg);

    const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
    rightLeg.position.set(0.3, -1.9, 0);
    rightLeg.castShadow = true;
    skeletonGroup.add(rightLeg);

    scene.add(skeletonGroup);
    skeletonRef.current = skeletonGroup;
  };

  /**
   * 添加网格和坐标轴
   */
  const addGridAndAxes = (scene) => {
    // 网格
    const gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x222222);
    scene.add(gridHelper);

    // 坐标轴
    const axesHelper = new THREE.AxesHelper(3);
    scene.add(axesHelper);
  };

  /**
   * 添加轨道控制器
   */
  const addControls = (camera, renderer) => {
    // 简单的鼠标控制实现
    let isMouseDown = false;
    let mouseX = 0;
    let mouseY = 0;
    let cameraDistance = 10;

    const onMouseDown = (event) => {
      isMouseDown = true;
      mouseX = event.clientX;
      mouseY = event.clientY;
    };

    const onMouseMove = (event) => {
      if (!isMouseDown) return;

      const deltaX = (event.clientX - mouseX) * 0.01;
      const deltaY = (event.clientY - mouseY) * 0.01;

      // 计算新的相机位置
      const spherical = new THREE.Spherical();
      spherical.setFromVector3(camera.position);
      spherical.theta -= deltaX;
      spherical.phi += deltaY;
      spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, spherical.phi));

      camera.position.setFromSpherical(spherical);
      camera.lookAt(0, 0, 0);

      mouseX = event.clientX;
      mouseY = event.clientY;
    };

    const onMouseUp = () => {
      isMouseDown = false;
    };

    const onWheel = (event) => {
      cameraDistance += event.deltaY * 0.01;
      cameraDistance = Math.max(2, Math.min(20, cameraDistance));

      const direction = new THREE.Vector3();
      camera.getWorldDirection(direction);
      camera.position.copy(direction.multiplyScalar(-cameraDistance));
    };

    renderer.domElement.addEventListener('mousedown', onMouseDown);
    renderer.domElement.addEventListener('mousemove', onMouseMove);
    renderer.domElement.addEventListener('mouseup', onMouseUp);
    renderer.domElement.addEventListener('wheel', onWheel);
  };

  /**
   * 更新骨架姿态
   */
  const updateSkeletonPose = (posePoint) => {
    if (!skeletonRef.current) return;

    const { pitch, yaw, roll, x, y, z } = posePoint;

    // 更新位置
    skeletonRef.current.position.set(x, y, z);

    // 更新旋转（将角度转换为弧度）
    skeletonRef.current.rotation.set(
      pitch * Math.PI / 180,
      yaw * Math.PI / 180,
      roll * Math.PI / 180
    );
  };

  /**
   * 渲染循环
   */
  const animate = () => {
    if (!rendererRef.current || !sceneRef.current || !cameraRef.current) return;

    rendererRef.current.render(sceneRef.current, cameraRef.current);
    requestAnimationFrame(animate);
  };

  /**
   * 处理窗口大小变化
   */
  const handleResize = () => {
    if (!mountRef.current || !cameraRef.current || !rendererRef.current) return;

    const width = mountRef.current.clientWidth;
    const height = mountRef.current.clientHeight;

    cameraRef.current.aspect = width / height;
    cameraRef.current.updateProjectionMatrix();
    rendererRef.current.setSize(width, height);
  };

  /**
   * 截图功能
   */
  const handleScreenshot = () => {
    if (!rendererRef.current) return;

    const canvas = rendererRef.current.domElement;
    const link = document.createElement('a');
    link.download = `attitude-tracker-${Date.now()}.png`;
    link.href = canvas.toDataURL();
    link.click();
  };

  /**
   * 导出功能
   */
  const handleExport = () => {
    if (poseData.length === 0) return;

    const dataStr = JSON.stringify(poseData, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.download = `pose-data-${Date.now()}.json`;
    link.href = url;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  /**
   * 清理资源
   */
  const cleanup = () => {
    if (playbackControllerRef.current) {
      playbackControllerRef.current.destroy();
    }

    if (rendererRef.current && mountRef.current) {
      mountRef.current.removeChild(rendererRef.current.domElement);
      rendererRef.current.dispose();
    }
  };

  // 监听窗口大小变化
  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">❌</div>
          <h2 className="text-2xl font-bold text-white mb-2">加载失败</h2>
          <p className="text-gray-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 flex">
      {/* 3D视图区域 */}
      <div className="flex-1 relative">
        <div 
          ref={mountRef} 
          className="w-full h-screen"
          style={{ minHeight: '600px' }}
        />
        
        {/* 加载提示 */}
        {!isSceneReady && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-75">
            <div className="text-center">
              <div className="animate-spin text-4xl mb-4">⏳</div>
              <p className="text-white">正在初始化3D场景...</p>
            </div>
          </div>
        )}

        {/* 数据为空提示 */}
        {isSceneReady && poseData.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-75">
            <div className="text-center">
              <div className="text-6xl mb-4">📊</div>
              <h2 className="text-2xl font-bold text-white mb-2">暂无数据</h2>
              <p className="text-gray-400 mb-4">请先输入姿态数据</p>
              <button className="btn-primary">
                前往数据输入
              </button>
            </div>
          </div>
        )}
      </div>

      {/* 控制面板 */}
      {isSceneReady && poseData.length > 0 && (
        <div className="w-80 p-4">
          <ControlPanel
            playbackController={playbackControllerRef.current}
            onScreenshot={handleScreenshot}
            onExport={handleExport}
          />
        </div>
      )}
    </div>
  );
};

export default Viewer3D;
