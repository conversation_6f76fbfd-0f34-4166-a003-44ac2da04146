# 测试文件目录

这个目录包含了Attitude Tracker 3D项目的所有测试文件和调试工具。

## 📁 文件结构

### 📋 测试指南文档
- **TESTING_GUIDE.md** - 完整的功能测试指南和清单
- **SIDEBAR_FIX_SUMMARY.md** - 侧边栏功能修复总结文档
- **test-settings-tools.md** - 设置和工具功能测试指南
- **test-sidebar-fix.md** - 侧边栏修复测试指南

### 🧪 测试脚本
- **test-functionality.js** - 核心功能自动化测试脚本
- **test-sidebar-functions.js** - 侧边栏功能测试脚本

### 🌐 调试页面
- **debug-sidebar.html** - 侧边栏功能调试页面
- **verify-navigation.html** - 导航功能验证页面
- **verify-settings-tools.html** - 设置和工具功能验证页面
- **server-status.html** - 开发服务器状态检查页面

## 🚀 使用方法

### 1. 运行测试脚本
```bash
# 在项目根目录执行
node tests/test-functionality.js
node tests/test-sidebar-functions.js
```

### 2. 使用调试页面
在浏览器中打开HTML文件进行交互式测试：
```
file:///path/to/attitude-tracker-3d/tests/debug-sidebar.html
file:///path/to/attitude-tracker-3d/tests/verify-navigation.html
file:///path/to/attitude-tracker-3d/tests/verify-settings-tools.html
file:///path/to/attitude-tracker-3d/tests/server-status.html
```

### 3. 查看测试指南
参考Markdown文档了解详细的测试步骤和预期结果。

## 🎯 测试覆盖范围

### 功能测试
- ✅ 数据输入和解析
- ✅ 3D场景渲染
- ✅ 播放控制
- ✅ 导航系统
- ✅ 设置管理
- ✅ 工具功能

### 交互测试
- ✅ 侧边栏导航
- ✅ 模态框操作
- ✅ 文件上传下载
- ✅ 状态管理

### 兼容性测试
- ✅ 浏览器兼容性
- ✅ 响应式设计
- ✅ API支持检查

## 📞 使用说明

1. **开发阶段**: 使用测试脚本验证核心功能
2. **调试阶段**: 使用HTML调试页面进行交互式测试
3. **发布前**: 参考测试指南进行完整的功能验证

## 🔧 维护说明

- 新增功能时，请同步更新相关测试文件
- 修复bug后，请验证对应的测试用例
- 定期检查测试文件的有效性和准确性

---

**注意**: 这些测试文件仅用于开发和调试，不会包含在生产构建中。
