/**
 * 全局状态管理模块
 * 用途：存储和管理姿态数据，提供数据访问接口
 */

// 数据存储状态
let poseDataState = {
  currentData: [], // PosePoint[]
  currentPose: null, // 当前实时姿态点
  isLoaded: false,
  isRealTimeMode: false, // 是否为实时预览模式
  metadata: {
    totalPoints: 0,
    duration: 0,
    lastUpdated: null
  }
};

// 订阅者列表，用于状态变更通知
let subscribers = [];

/**
 * 存储姿态数据
 * @param {Array} poseList - PosePoint数组
 * @throws {Error} 当数据格式不正确时抛出错误
 */
export function setData(poseList) {
  if (!Array.isArray(poseList)) {
    throw new Error('数据必须是数组格式');
  }

  // 验证数据格式
  for (let i = 0; i < poseList.length; i++) {
    const point = poseList[i];
    if (!validatePosePoint(point)) {
      throw new Error(`第${i + 1}个数据点格式不正确`);
    }
  }

  // 更新状态
  poseDataState.currentData = [...poseList];
  poseDataState.isLoaded = true;
  poseDataState.metadata = {
    totalPoints: poseList.length,
    duration: poseList.length > 0 ? 
      poseList[poseList.length - 1].timestamp - poseList[0].timestamp : 0,
    lastUpdated: new Date()
  };

  // 通知订阅者
  notifySubscribers();
}

/**
 * 获取姿态数据
 * @returns {Array} PosePoint数组
 */
export function getData() {
  return [...poseDataState.currentData];
}

/**
 * 获取数据元信息
 * @returns {Object} 包含数据统计信息的对象
 */
export function getMetadata() {
  return { ...poseDataState.metadata };
}

/**
 * 检查数据是否已加载
 * @returns {boolean} 数据加载状态
 */
export function isDataLoaded() {
  return poseDataState.isLoaded;
}

/**
 * 清空数据
 */
export function clearData() {
  poseDataState.currentData = [];
  poseDataState.isLoaded = false;
  poseDataState.metadata = {
    totalPoints: 0,
    duration: 0,
    lastUpdated: null
  };
  notifySubscribers();
}

/**
 * 订阅数据变更
 * @param {Function} callback - 数据变更时的回调函数
 * @returns {Function} 取消订阅的函数
 */
export function subscribe(callback) {
  if (typeof callback !== 'function') {
    throw new Error('回调函数必须是function类型');
  }
  
  subscribers.push(callback);
  
  // 返回取消订阅函数
  return () => {
    const index = subscribers.indexOf(callback);
    if (index > -1) {
      subscribers.splice(index, 1);
    }
  };
}

/**
 * 验证PosePoint数据格式
 * @param {Object} point - 待验证的数据点
 * @returns {boolean} 验证结果
 */
function validatePosePoint(point) {
  if (!point || typeof point !== 'object') {
    return false;
  }

  const requiredFields = ['timestamp', 'pitch', 'yaw', 'roll', 'x', 'y', 'z'];
  
  for (const field of requiredFields) {
    if (typeof point[field] !== 'number' || isNaN(point[field])) {
      return false;
    }
  }

  return true;
}

/**
 * 设置实时姿态数据（用于实时预览）
 * @param {Object} posePoint - 单个PosePoint对象
 * @throws {Error} 当数据格式不正确时抛出错误
 */
export function setRealTimePose(posePoint) {
  if (!validatePosePoint(posePoint)) {
    throw new Error('实时姿态数据格式不正确');
  }

  // 更新实时姿态状态
  poseDataState.currentPose = { ...posePoint };
  poseDataState.isRealTimeMode = true;
  poseDataState.metadata.lastUpdated = new Date();

  // 通知订阅者
  notifySubscribers();
}

/**
 * 获取当前实时姿态
 * @returns {Object|null} 当前实时姿态点
 */
export function getCurrentPose() {
  return poseDataState.currentPose ? { ...poseDataState.currentPose } : null;
}

/**
 * 检查是否为实时预览模式
 * @returns {boolean} 实时预览模式状态
 */
export function isRealTimeMode() {
  return poseDataState.isRealTimeMode;
}

/**
 * 退出实时预览模式
 */
export function exitRealTimeMode() {
  poseDataState.isRealTimeMode = false;
  poseDataState.currentPose = null;
  notifySubscribers();
}

/**
 * 通知所有订阅者数据已变更
 */
function notifySubscribers() {
  subscribers.forEach(callback => {
    callback(poseDataState);
  });
}
