<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="基于Web的三维姿态追踪系统，支持实时数据可视化、动画播放和轨迹分析" />
  <meta name="keywords" content="3D, 姿态追踪, Three.js, React, 可视化" />
  <meta name="author" content="Attitude Tracker 3D Team" />
  
  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="Attitude Tracker 3D - 三维姿态追踪系统" />
  <meta property="og:description" content="基于Web的三维姿态追踪系统，支持实时数据可视化、动画播放和轨迹分析" />
  <meta property="og:type" content="website" />
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="alternate icon" href="/favicon.ico" />
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Title -->
  <title>Attitude Tracker 3D - 三维姿态追踪系统</title>
  
  <!-- Theme color for mobile browsers -->
  <meta name="theme-color" content="#1e3a8a" />
  
  <!-- Apple specific meta tags -->
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="apple-mobile-web-app-title" content="Attitude Tracker 3D" />
  
  <!-- Microsoft specific meta tags -->
  <meta name="msapplication-TileColor" content="#1e3a8a" />
  
  <!-- Prevent zoom on mobile -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  
  <style>
    /* Critical CSS for loading screen */
    #loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      font-family: 'Inter', system-ui, sans-serif;
    }
    
    .loading-content {
      text-align: center;
      color: white;
    }
    
    .loading-logo {
      width: 80px;
      height: 80px;
      background: #3b82f6;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 24px;
      font-size: 32px;
      font-weight: bold;
      animation: pulse 2s infinite;
    }
    
    .loading-title {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 8px;
    }
    
    .loading-subtitle {
      font-size: 16px;
      color: #94a3b8;
      margin-bottom: 32px;
    }
    
    .loading-dots {
      display: flex;
      justify-content: center;
      gap: 8px;
    }
    
    .loading-dot {
      width: 12px;
      height: 12px;
      background: #3b82f6;
      border-radius: 50%;
      animation: bounce 1.4s infinite ease-in-out both;
    }
    
    .loading-dot:nth-child(1) { animation-delay: -0.32s; }
    .loading-dot:nth-child(2) { animation-delay: -0.16s; }
    .loading-dot:nth-child(3) { animation-delay: 0s; }
    
    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }
    
    @keyframes bounce {
      0%, 80%, 100% {
        transform: scale(0);
      }
      40% {
        transform: scale(1);
      }
    }
    
    /* Hide loading screen when app is ready */
    .app-ready #loading-screen {
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.5s ease-out;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading-screen">
    <div class="loading-content">
      <div class="loading-logo">3D</div>
      <h1 class="loading-title">Attitude Tracker 3D</h1>
      <p class="loading-subtitle">三维姿态追踪系统</p>
      <div class="loading-dots">
        <div class="loading-dot"></div>
        <div class="loading-dot"></div>
        <div class="loading-dot"></div>
      </div>
    </div>
  </div>
  
  <!-- React App Root -->
  <div id="root"></div>
  
  <!-- App Script -->
  <script type="module" src="/src/main.jsx"></script>
  
  <!-- Remove loading screen when app is ready -->
  <script>
    // Remove loading screen after a delay to ensure app is mounted
    window.addEventListener('load', function() {
      setTimeout(function() {
        document.body.classList.add('app-ready');
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.remove();
          }
        }, 500);
      }, 1000);
    });
    
    // Fallback: remove loading screen after 5 seconds
    setTimeout(function() {
      const loadingScreen = document.getElementById('loading-screen');
      if (loadingScreen) {
        loadingScreen.remove();
      }
    }, 5000);
  </script>
  
  <!-- Error handling for older browsers -->
  <script>
    // Check for basic browser support
    if (!window.fetch || !window.Promise || !window.Map) {
      document.getElementById('loading-screen').innerHTML = 
        '<div class="loading-content">' +
        '<h1 style="color: #ef4444; margin-bottom: 16px;">浏览器不支持</h1>' +
        '<p style="color: #94a3b8;">请使用现代浏览器访问此应用</p>' +
        '</div>';
    }
  </script>
</body>
</html>
