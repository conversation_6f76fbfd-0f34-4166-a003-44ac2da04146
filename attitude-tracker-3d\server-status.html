<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .loading { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.success {
            background-color: #28a745;
        }
        .button.success:hover {
            background-color: #218838;
        }
        #status-indicator {
            font-size: 48px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Attitude Tracker 3D 服务器状态</h1>
        
        <div id="status-indicator">⏳</div>
        
        <div id="status-message" class="status loading">
            正在检查服务器状态...
        </div>

        <div id="server-info" style="display: none;">
            <h3>服务器信息</h3>
            <p><strong>地址:</strong> <span id="server-url"></span></p>
            <p><strong>状态:</strong> <span id="server-status"></span></p>
            <p><strong>检查时间:</strong> <span id="check-time"></span></p>
        </div>

        <div id="action-buttons" style="display: none;">
            <a href="http://127.0.0.1:3001/" target="_blank" class="button success">
                🚀 打开应用
            </a>
            <button onclick="checkStatus()" class="button">
                🔄 重新检查
            </button>
        </div>

        <div id="troubleshooting" style="display: none; text-align: left; margin-top: 30px;">
            <h3>🔍 故障排除</h3>
            <div class="status error">
                <strong>如果服务器无法访问，请尝试：</strong>
                <ol style="margin: 10px 0; padding-left: 20px;">
                    <li>检查命令行窗口是否显示服务器正在运行</li>
                    <li>在项目目录中重新运行: <code>npm run dev</code></li>
                    <li>检查端口3001是否被其他程序占用</li>
                    <li>尝试使用不同的端口</li>
                    <li>重启命令行并重新启动服务器</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        function checkStatus() {
            const statusIndicator = document.getElementById('status-indicator');
            const statusMessage = document.getElementById('status-message');
            const serverInfo = document.getElementById('server-info');
            const actionButtons = document.getElementById('action-buttons');
            const troubleshooting = document.getElementById('troubleshooting');
            
            // 重置状态
            statusIndicator.textContent = '⏳';
            statusMessage.className = 'status loading';
            statusMessage.textContent = '正在检查服务器状态...';
            serverInfo.style.display = 'none';
            actionButtons.style.display = 'none';
            troubleshooting.style.display = 'none';
            
            const serverUrl = 'http://127.0.0.1:3001/';
            const checkTime = new Date().toLocaleString('zh-CN');
            
            fetch(serverUrl)
                .then(response => {
                    if (response.ok) {
                        // 服务器正常
                        statusIndicator.textContent = '✅';
                        statusMessage.className = 'status success';
                        statusMessage.textContent = '服务器运行正常！';
                        
                        document.getElementById('server-url').textContent = serverUrl;
                        document.getElementById('server-status').textContent = '正常运行';
                        document.getElementById('check-time').textContent = checkTime;
                        
                        serverInfo.style.display = 'block';
                        actionButtons.style.display = 'block';
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .catch(error => {
                    // 服务器异常
                    statusIndicator.textContent = '❌';
                    statusMessage.className = 'status error';
                    statusMessage.textContent = `服务器无法访问: ${error.message}`;
                    
                    document.getElementById('server-url').textContent = serverUrl;
                    document.getElementById('server-status').textContent = '无法连接';
                    document.getElementById('check-time').textContent = checkTime;
                    
                    serverInfo.style.display = 'block';
                    troubleshooting.style.display = 'block';
                    
                    // 显示重新检查按钮
                    actionButtons.style.display = 'block';
                    actionButtons.innerHTML = `
                        <button onclick="checkStatus()" class="button">
                            🔄 重新检查
                        </button>
                    `;
                });
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', checkStatus);
        
        // 每30秒自动检查一次
        setInterval(checkStatus, 30000);
    </script>
</body>
</html>
