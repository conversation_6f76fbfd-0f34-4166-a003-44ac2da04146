/**
 * 简化版应用组件 - 用于测试基本功能
 */

import React, { useState } from 'react';

const SimpleApp = () => {
  const [currentPage, setCurrentPage] = useState('home');

  const pages = {
    home: <HomePage onNavigate={setCurrentPage} />,
    test: <TestPage onNavigate={setCurrentPage} />
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* 简单导航 */}
      <nav className="bg-gray-800 p-4">
        <div className="flex space-x-4">
          <button
            onClick={() => setCurrentPage('home')}
            className={`px-4 py-2 rounded ${currentPage === 'home' ? 'bg-blue-600' : 'bg-gray-600'}`}
          >
            首页
          </button>
          <button
            onClick={() => setCurrentPage('test')}
            className={`px-4 py-2 rounded ${currentPage === 'test' ? 'bg-blue-600' : 'bg-gray-600'}`}
          >
            测试页
          </button>
        </div>
      </nav>

      {/* 页面内容 */}
      <main className="p-8">
        {pages[currentPage] || <div>页面未找到</div>}
      </main>
    </div>
  );
};

const HomePage = ({ onNavigate }) => {
  return (
    <div className="text-center">
      <h1 className="text-4xl font-bold mb-8">Attitude Tracker 3D</h1>
      <p className="text-xl mb-8">三维姿态追踪系统 - 简化测试版</p>
      
      <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-xl font-semibold mb-4">✅ 系统状态</h3>
          <ul className="text-left space-y-2">
            <li>✅ React 应用正常运行</li>
            <li>✅ Tailwind CSS 样式加载</li>
            <li>✅ 组件渲染正常</li>
            <li>✅ 状态管理工作</li>
          </ul>
        </div>
        
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-xl font-semibold mb-4">🧪 功能测试</h3>
          <button
            onClick={() => onNavigate('test')}
            className="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-medium"
          >
            进入测试页面
          </button>
        </div>
      </div>
    </div>
  );
};

const TestPage = ({ onNavigate }) => {
  const [testResults, setTestResults] = useState([]);

  const runTests = () => {
    const results = [];
    
    // 测试1: 基本JavaScript功能
    try {
      const testData = { x: 1, y: 2, z: 3 };
      const jsonString = JSON.stringify(testData);
      const parsed = JSON.parse(jsonString);
      results.push({ name: 'JSON处理', status: 'success', message: '✅ 正常' });
    } catch (e) {
      results.push({ name: 'JSON处理', status: 'error', message: '❌ 失败: ' + e.message });
    }

    // 测试2: 数学计算
    try {
      const angle = 45;
      const radians = angle * Math.PI / 180;
      const sin = Math.sin(radians);
      results.push({ name: '数学计算', status: 'success', message: `✅ sin(45°) = ${sin.toFixed(3)}` });
    } catch (e) {
      results.push({ name: '数学计算', status: 'error', message: '❌ 失败: ' + e.message });
    }

    // 测试3: 数组操作
    try {
      const data = [1, 2, 3, 4, 5];
      const filtered = data.filter(x => x > 2);
      const mapped = filtered.map(x => x * 2);
      results.push({ name: '数组操作', status: 'success', message: `✅ 结果: [${mapped.join(', ')}]` });
    } catch (e) {
      results.push({ name: '数组操作', status: 'error', message: '❌ 失败: ' + e.message });
    }

    // 测试4: 时间处理
    try {
      const now = new Date();
      const timestamp = now.getTime();
      results.push({ name: '时间处理', status: 'success', message: `✅ 时间戳: ${timestamp}` });
    } catch (e) {
      results.push({ name: '时间处理', status: 'error', message: '❌ 失败: ' + e.message });
    }

    setTestResults(results);
  };

  return (
    <div>
      <h2 className="text-3xl font-bold mb-6">功能测试页面</h2>
      
      <div className="mb-6">
        <button
          onClick={runTests}
          className="bg-green-600 hover:bg-green-700 px-6 py-3 rounded-lg font-medium mr-4"
        >
          运行测试
        </button>
        <button
          onClick={() => onNavigate('home')}
          className="bg-gray-600 hover:bg-gray-700 px-6 py-3 rounded-lg font-medium"
        >
          返回首页
        </button>
      </div>

      {testResults.length > 0 && (
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-xl font-semibold mb-4">测试结果</h3>
          <div className="space-y-3">
            {testResults.map((result, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-gray-700 rounded">
                <span className="font-medium">{result.name}</span>
                <span className={result.status === 'success' ? 'text-green-400' : 'text-red-400'}>
                  {result.message}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="mt-8 bg-gray-800 p-6 rounded-lg">
        <h3 className="text-xl font-semibold mb-4">浏览器信息</h3>
        <div className="grid md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong>用户代理:</strong><br />
            <span className="text-gray-300">{navigator.userAgent}</span>
          </div>
          <div>
            <strong>屏幕分辨率:</strong><br />
            <span className="text-gray-300">{screen.width} x {screen.height}</span>
          </div>
          <div>
            <strong>窗口大小:</strong><br />
            <span className="text-gray-300">{window.innerWidth} x {window.innerHeight}</span>
          </div>
          <div>
            <strong>语言:</strong><br />
            <span className="text-gray-300">{navigator.language}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleApp;
