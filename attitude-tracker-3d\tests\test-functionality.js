/**
 * 功能测试脚本
 * 用于验证核心模块的功能
 */

// 测试数据解析模块
import { parseCSV, parseManualInput, convertAngleUnits } from './src/services/dataParser.js';

// 测试数据存储模块
import { setData, getData, clearData, subscribe } from './src/store/dataStore.js';

// 测试播放控制器
import { createPlaybackController } from './src/utils/playback.js';

console.log('🚀 开始功能测试...\n');

// 1. 测试数据解析功能
console.log('1️⃣ 测试数据解析功能');
try {
  // 测试手动输入解析
  const manualData = {
    pitch: 10.5,
    yaw: -5.2,
    roll: 2.1,
    x: 0.5,
    y: 1.2,
    z: 0.8
  };
  
  const parsedManual = parseManualInput(manualData);
  console.log('✅ 手动输入解析成功:', parsedManual);

  // 测试CSV解析
  const csvData = `timestamp,pitch,yaw,roll,x,y,z
1640995200000,10.5,-5.2,2.1,0.5,1.2,0.8
1640995200100,11.2,-4.8,2.3,0.6,1.3,0.9
1640995200200,12.1,-4.5,2.5,0.7,1.4,1.0`;

  const parsedCSV = parseCSV(csvData);
  console.log('✅ CSV解析成功，数据点数:', parsedCSV.length);

  // 测试角度单位转换
  const convertedData = convertAngleUnits(parsedCSV, 'degrees', 'radians');
  console.log('✅ 角度单位转换成功');

} catch (error) {
  console.error('❌ 数据解析测试失败:', error.message);
}

// 2. 测试数据存储功能
console.log('\n2️⃣ 测试数据存储功能');
try {
  // 清空数据
  clearData();
  console.log('✅ 数据清空成功');

  // 测试数据存储
  const testData = [
    { timestamp: 1000, pitch: 10, yaw: 20, roll: 30, x: 1, y: 2, z: 3 },
    { timestamp: 2000, pitch: 15, yaw: 25, roll: 35, x: 1.5, y: 2.5, z: 3.5 }
  ];

  setData(testData);
  console.log('✅ 数据存储成功');

  // 测试数据获取
  const retrievedData = getData();
  console.log('✅ 数据获取成功，数据点数:', retrievedData.length);

  // 测试订阅功能
  let subscriptionCalled = false;
  const unsubscribe = subscribe((state) => {
    subscriptionCalled = true;
    console.log('✅ 订阅回调触发，数据点数:', state.currentData.length);
  });

  // 触发订阅
  setData([...testData, { timestamp: 3000, pitch: 20, yaw: 30, roll: 40, x: 2, y: 3, z: 4 }]);
  
  if (subscriptionCalled) {
    console.log('✅ 订阅功能正常');
  }

  unsubscribe();

} catch (error) {
  console.error('❌ 数据存储测试失败:', error.message);
}

// 3. 测试播放控制器
console.log('\n3️⃣ 测试播放控制器功能');
try {
  const controller = createPlaybackController();
  console.log('✅ 播放控制器创建成功');

  // 设置测试数据
  const playbackData = [
    { timestamp: 0, pitch: 0, yaw: 0, roll: 0, x: 0, y: 0, z: 0 },
    { timestamp: 100, pitch: 10, yaw: 10, roll: 10, x: 1, y: 1, z: 1 },
    { timestamp: 200, pitch: 20, yaw: 20, roll: 20, x: 2, y: 2, z: 2 }
  ];

  controller.setData(playbackData);
  console.log('✅ 播放数据设置成功');

  // 测试播放状态
  const status = controller.getStatus();
  console.log('✅ 播放状态获取成功:', {
    totalFrames: status.totalFrames,
    isPlaying: status.isPlaying,
    progress: status.progress
  });

  // 测试速度设置
  controller.setSpeed(2.0);
  console.log('✅ 播放速度设置成功');

  // 测试跳转
  controller.seekTo(1);
  console.log('✅ 播放位置跳转成功');

  controller.destroy();
  console.log('✅ 播放控制器销毁成功');

} catch (error) {
  console.error('❌ 播放控制器测试失败:', error.message);
}

// 4. 测试错误处理
console.log('\n4️⃣ 测试错误处理');
try {
  // 测试无效数据
  try {
    parseManualInput({ invalid: 'data' });
    console.error('❌ 应该抛出错误但没有');
  } catch (e) {
    console.log('✅ 无效数据错误处理正常:', e.message);
  }

  // 测试无效CSV
  try {
    parseCSV('invalid,csv,format');
    console.error('❌ 应该抛出错误但没有');
  } catch (e) {
    console.log('✅ 无效CSV错误处理正常:', e.message);
  }

  // 测试无效数据存储
  try {
    setData('not an array');
    console.error('❌ 应该抛出错误但没有');
  } catch (e) {
    console.log('✅ 无效数据存储错误处理正常:', e.message);
  }

} catch (error) {
  console.error('❌ 错误处理测试失败:', error.message);
}

console.log('\n🎉 功能测试完成！');
console.log('\n📋 测试总结:');
console.log('- ✅ 数据解析模块：支持手动输入和CSV解析');
console.log('- ✅ 数据存储模块：支持数据存储、获取和订阅');
console.log('- ✅ 播放控制器：支持播放控制和状态管理');
console.log('- ✅ 错误处理：正确处理各种异常情况');
console.log('\n🌐 应用已启动在: http://localhost:3000');
console.log('请在浏览器中测试以下功能:');
console.log('1. 首页导航和介绍');
console.log('2. 数据输入页面的手动输入和文件上传');
console.log('3. 3D展示页面的模型渲染和动画');
console.log('4. 历史记录页面的数据管理');
console.log('5. 控制面板的播放控制功能');
