/**
 * 数据解析服务模块
 * 用途：解析CSV文件和用户输入数据为PosePoint结构
 */

/**
 * 解析CSV文件内容为PosePoint数组
 * @param {string} fileContent - CSV文件内容
 * @returns {Array} PosePoint数组
 * @throws {Error} 当CSV格式不正确时抛出错误
 */
export function parseCSV(fileContent) {
  if (typeof fileContent !== 'string') {
    throw new Error('文件内容必须是字符串格式');
  }

  const lines = fileContent.trim().split('\n');
  
  if (lines.length === 0) {
    throw new Error('CSV文件为空');
  }

  // 解析表头
  const headers = parseCSVLine(lines[0]);
  const expectedHeaders = ['timestamp', 'pitch', 'yaw', 'roll', 'x', 'y', 'z'];
  
  if (!validateHeaders(headers, expectedHeaders)) {
    throw new Error(`CSV表头格式不正确。期望: ${expectedHeaders.join(', ')}`);
  }

  // 解析数据行
  const posePoints = [];
  
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line === '') continue; // 跳过空行
    
    const values = parseCSVLine(line);
    
    if (values.length !== expectedHeaders.length) {
      throw new Error(`第${i + 1}行数据列数不正确，期望${expectedHeaders.length}列，实际${values.length}列`);
    }

    const posePoint = createPosePoint(headers, values, i + 1);
    posePoints.push(posePoint);
  }

  if (posePoints.length === 0) {
    throw new Error('CSV文件中没有有效的数据行');
  }

  return posePoints;
}

/**
 * 解析用户手动输入的数据
 * @param {Object} inputData - 用户输入的数据对象
 * @returns {Object} PosePoint对象
 * @throws {Error} 当输入数据格式不正确时抛出错误
 */
export function parseManualInput(inputData) {
  if (!inputData || typeof inputData !== 'object') {
    throw new Error('输入数据必须是对象格式');
  }

  const requiredFields = ['pitch', 'yaw', 'roll', 'x', 'y', 'z'];
  const posePoint = {
    timestamp: inputData.timestamp || Date.now()
  };

  for (const field of requiredFields) {
    const value = parseFloat(inputData[field]);
    
    if (isNaN(value)) {
      throw new Error(`${field}字段必须是有效的数字`);
    }
    
    posePoint[field] = value;
  }

  return posePoint;
}

/**
 * 角度单位转换：度转弧度
 * @param {number} degrees - 角度值（度）
 * @returns {number} 弧度值
 */
export function degreesToRadians(degrees) {
  return degrees * (Math.PI / 180);
}

/**
 * 角度单位转换：弧度转度
 * @param {number} radians - 弧度值
 * @returns {number} 角度值（度）
 */
export function radiansToDegrees(radians) {
  return radians * (180 / Math.PI);
}

/**
 * 批量转换角度单位
 * @param {Array} posePoints - PosePoint数组
 * @param {string} fromUnit - 源单位 ('degrees' | 'radians')
 * @param {string} toUnit - 目标单位 ('degrees' | 'radians')
 * @returns {Array} 转换后的PosePoint数组
 */
export function convertAngleUnits(posePoints, fromUnit, toUnit) {
  if (fromUnit === toUnit) {
    return posePoints;
  }

  const converter = fromUnit === 'degrees' ? degreesToRadians : radiansToDegrees;
  
  return posePoints.map(point => ({
    ...point,
    pitch: converter(point.pitch),
    yaw: converter(point.yaw),
    roll: converter(point.roll)
  }));
}

/**
 * 解析CSV行，处理逗号分隔和引号包围的值
 * @param {string} line - CSV行内容
 * @returns {Array} 解析后的值数组
 */
function parseCSVLine(line) {
  const values = [];
  let current = '';
  let inQuotes = false;
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i];
    
    if (char === '"') {
      inQuotes = !inQuotes;
    } else if (char === ',' && !inQuotes) {
      values.push(current.trim());
      current = '';
    } else {
      current += char;
    }
  }
  
  values.push(current.trim());
  return values;
}

/**
 * 验证CSV表头是否正确
 * @param {Array} headers - 实际表头
 * @param {Array} expected - 期望表头
 * @returns {boolean} 验证结果
 */
function validateHeaders(headers, expected) {
  if (headers.length !== expected.length) {
    return false;
  }
  
  for (let i = 0; i < expected.length; i++) {
    if (headers[i].toLowerCase() !== expected[i].toLowerCase()) {
      return false;
    }
  }
  
  return true;
}

/**
 * 根据表头和值创建PosePoint对象
 * @param {Array} headers - 表头数组
 * @param {Array} values - 值数组
 * @param {number} lineNumber - 行号（用于错误提示）
 * @returns {Object} PosePoint对象
 */
function createPosePoint(headers, values, lineNumber) {
  const posePoint = {};
  
  for (let i = 0; i < headers.length; i++) {
    const header = headers[i].toLowerCase();
    const value = parseFloat(values[i]);
    
    if (isNaN(value)) {
      throw new Error(`第${lineNumber}行${header}字段值"${values[i]}"不是有效数字`);
    }
    
    posePoint[header] = value;
  }
  
  return posePoint;
}
